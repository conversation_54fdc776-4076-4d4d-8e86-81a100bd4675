#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FPS AI自瞄控制器测试界面
专门负责UI界面的创建和管理
"""

import tkinter as tk
from tkinter import ttk
import math
from dataclasses import dataclass


@dataclass 
class TestConfig:
    """测试程序配置参数"""
    inference_interval_ms: int = 30  # AI推理间隔毫秒数


class AimTestUI:
    """AI自瞄测试界面类
    
    负责创建和管理所有UI组件，包括：
    - 测试画布
    - 参数控制面板
    - 统计信息显示
    - 控制按钮
    """
    
    def __init__(self, title="FPS AI自瞄控制器测试程序", geometry="1000x700"):
        """
        初始化UI界面
        
        Args:
            title: 窗口标题
            geometry: 窗口大小
        """
        self.root = tk.Tk()
        self.root.title(title)
        self.root.geometry(geometry)
        
        # 配置参数
        self.test_config = TestConfig()
        
        # UI组件引用
        self.canvas = None
        self.stats_text = None
        
        # 参数控制变量
        self.inference_interval_var = None
        self.speed_var = None
        self.smooth_var = None
        self.prediction_var = None
        
        # 参数标签引用
        self.inference_interval_label = None
        self.speed_label = None
        self.smooth_label = None
        
        # 控制按钮引用
        self.start_button = None
        self.stop_button = None
        
        # 回调函数
        self.on_mouse_move_callback = None
        self.on_start_test_callback = None
        self.on_stop_test_callback = None
        self.on_reset_stats_callback = None
        self.on_save_config_callback = None
        self.on_config_change_callback = None
        
        # 创建界面
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 画布区域
        canvas_frame = ttk.LabelFrame(main_frame, text="测试区域")
        canvas_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        self.canvas = tk.Canvas(canvas_frame, bg="black", width=600, height=500)
        self.canvas.pack(padx=10, pady=10)
        
        # 控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板")
        control_frame.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 参数调节
        self.setup_parameter_controls(control_frame)
        
        # 统计信息
        self.setup_stats_display(control_frame)
        
        # 控制按钮
        self.setup_control_buttons(control_frame)
    
    def setup_parameter_controls(self, parent):
        """设置参数控制界面"""
        params_frame = ttk.LabelFrame(parent, text="参数设置")
        params_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 推理间隔设置
        self.inference_interval_label = ttk.Label(params_frame, text=f"推理间隔: {self.test_config.inference_interval_ms}ms")
        self.inference_interval_label.pack(anchor=tk.W)
        self.inference_interval_var = tk.IntVar(value=self.test_config.inference_interval_ms)
        interval_scale = ttk.Scale(params_frame, from_=10, to=100, 
                                  variable=self.inference_interval_var, orient=tk.HORIZONTAL,
                                  command=self.on_inference_interval_change)
        interval_scale.pack(fill=tk.X, padx=5)
        
        # 移动速度
        self.speed_label = ttk.Label(params_frame, text="移动速度: 0.80")
        self.speed_label.pack(anchor=tk.W)
        self.speed_var = tk.DoubleVar(value=0.8)
        speed_scale = ttk.Scale(params_frame, from_=0.1, to=2.0, 
                               variable=self.speed_var, orient=tk.HORIZONTAL,
                               command=self.on_speed_change)
        speed_scale.pack(fill=tk.X, padx=5)
        
        # 平滑系数
        self.smooth_label = ttk.Label(params_frame, text="平滑系数: 0.30")
        self.smooth_label.pack(anchor=tk.W)
        self.smooth_var = tk.DoubleVar(value=0.3)
        smooth_scale = ttk.Scale(params_frame, from_=0.1, to=1.0, 
                                variable=self.smooth_var, orient=tk.HORIZONTAL,
                                command=self.on_smooth_change)
        smooth_scale.pack(fill=tk.X, padx=5)
        
        # 预测开关
        self.prediction_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(params_frame, text="启用预测", 
                       variable=self.prediction_var,
                       command=self.on_prediction_change).pack(anchor=tk.W, pady=5)
    
    def setup_stats_display(self, parent):
        """设置统计信息显示"""
        stats_frame = ttk.LabelFrame(parent, text="实时统计")
        stats_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.stats_text = tk.Text(stats_frame, height=12, width=30, font=("Consolas", 9))
        self.stats_text.pack(padx=5, pady=5)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(stats_frame, orient="vertical", command=self.stats_text.yview)
        scrollbar.pack(side="right", fill="y")
        self.stats_text.configure(yscrollcommand=scrollbar.set)
    
    def setup_control_buttons(self, parent):
        """设置控制按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, padx=5, pady=10)
        
        self.start_button = ttk.Button(button_frame, text="开始测试", 
                                      command=self.on_start_test)
        self.start_button.pack(fill=tk.X, pady=2)
        
        self.stop_button = ttk.Button(button_frame, text="停止测试", 
                                     command=self.on_stop_test, state=tk.DISABLED)
        self.stop_button.pack(fill=tk.X, pady=2)
        
        ttk.Button(button_frame, text="重置统计", 
                  command=self.on_reset_stats).pack(fill=tk.X, pady=2)
        
        ttk.Button(button_frame, text="保存配置", 
                  command=self.on_save_config).pack(fill=tk.X, pady=2)
    
    def on_inference_interval_change(self, value):
        """推理间隔变化回调"""
        interval_ms = int(float(value))
        self.inference_interval_label.config(text=f"推理间隔: {interval_ms}ms")
        self.test_config.inference_interval_ms = interval_ms
        if self.on_config_change_callback:
            self.on_config_change_callback()
    
    def on_speed_change(self, value):
        """移动速度变化回调"""
        speed = float(value)
        self.speed_label.config(text=f"移动速度: {speed:.2f}")
        if self.on_config_change_callback:
            self.on_config_change_callback()
    
    def on_smooth_change(self, value):
        """平滑系数变化回调"""
        smooth = float(value)
        self.smooth_label.config(text=f"平滑系数: {smooth:.2f}")
        if self.on_config_change_callback:
            self.on_config_change_callback()
    
    def on_prediction_change(self):
        """预测开关变化回调"""
        if self.on_config_change_callback:
            self.on_config_change_callback()
    
    def on_start_test(self):
        """开始测试按钮回调"""
        if self.on_start_test_callback:
            self.on_start_test_callback()
    
    def on_stop_test(self):
        """停止测试按钮回调"""
        if self.on_stop_test_callback:
            self.on_stop_test_callback()
    
    def on_reset_stats(self):
        """重置统计按钮回调"""
        if self.on_reset_stats_callback:
            self.on_reset_stats_callback()
    
    def on_save_config(self):
        """保存配置按钮回调"""
        if self.on_save_config_callback:
            self.on_save_config_callback()
    
    def set_button_states(self, start_enabled: bool, stop_enabled: bool):
        """设置按钮状态"""
        self.start_button.config(state=tk.NORMAL if start_enabled else tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL if stop_enabled else tk.DISABLED)
    
    def bind_mouse_events(self):
        """绑定鼠标事件"""
        if self.on_mouse_move_callback:
            self.canvas.bind("<Motion>", self.on_mouse_move_callback)
    
    def get_config_values(self) -> dict:
        """获取当前配置值"""
        return {
            "inference_interval_ms": self.inference_interval_var.get(),
            "move_speed": self.speed_var.get(),
            "smoothing_factor": self.smooth_var.get(),
            "prediction_enabled": self.prediction_var.get()
        }
    
    def update_stats_display(self, stats_text: str):
        """更新统计信息显示"""
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text)
    
    def clear_canvas(self):
        """清空画布"""
        self.canvas.delete("all")
    
    def draw_grid(self):
        """绘制网格"""
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        # 绘制网格线
        for i in range(0, canvas_width, 50):
            self.canvas.create_line(i, 0, i, canvas_height, 
                                   fill="gray20", width=1)
        for i in range(0, canvas_height, 50):
            self.canvas.create_line(0, i, canvas_width, i, 
                                   fill="gray20", width=1)
    
    def draw_point(self, x: float, y: float, color: str, size: int = 8, label: str = ""):
        """绘制点"""
        self.canvas.create_oval(x-size, y-size, x+size, y+size, 
                               fill=color, outline="white", width=2)
        if label:
            self.canvas.create_text(x, y-20, text=label, fill=color, font=("Arial", 10))
    
    def draw_line(self, x1: float, y1: float, x2: float, y2: float, 
                  color: str = "cyan", width: int = 1, dash=None):
        """绘制线条"""
        self.canvas.create_line(x1, y1, x2, y2, fill=color, width=width, dash=dash)
    
    def draw_text(self, x: float, y: float, text: str, color: str = "white", 
                  font=("Arial", 12), anchor=tk.NW):
        """绘制文本"""
        self.canvas.create_text(x, y, anchor=anchor, text=text, fill=color, font=font)
    
    def run(self):
        """运行UI主循环"""
        self.root.mainloop()
    
    def destroy(self):
        """销毁窗口"""
        self.root.destroy()
    
    def after(self, delay: int, callback):
        """延时调用"""
        return self.root.after(delay, callback)
    
    def set_close_callback(self, callback):
        """设置窗口关闭回调"""
        self.root.protocol("WM_DELETE_WINDOW", callback)

    def update_test_display(self, target_pos, aim_pos, inference_pos, inference_stats=None):
        """
        更新测试显示界面

        Args:
            target_pos: 目标位置 (x, y)
            aim_pos: 瞄准位置 (x, y)
            inference_pos: 推理位置 (x, y)
            inference_stats: 推理统计信息字典
        """
        self.clear_canvas()

        # 绘制网格
        self.draw_grid()

        # 绘制目标点（绿色，跟随鼠标）
        self.draw_point(target_pos[0], target_pos[1], "green", 8, "实时目标")

        # 绘制AI推理结果点（黄色，每30ms更新一次的位置）
        self.draw_point(inference_pos[0], inference_pos[1], "yellow", 6, "AI推理")

        # 绘制AI瞄准点（红色）
        self.draw_point(aim_pos[0], aim_pos[1], "red", 8, "AI瞄准")

        # 绘制连接线
        self.draw_line(inference_pos[0], inference_pos[1],
                      aim_pos[0], aim_pos[1], "cyan", 1, (5, 5))

        # 显示距离
        import math
        distance = math.sqrt((target_pos[0] - aim_pos[0])**2 +
                           (target_pos[1] - aim_pos[1])**2)
        self.draw_text(10, 10, f"实时距离: {distance:.1f}px")

        # 显示推理统计
        if inference_stats:
            self.draw_text(10, 30,
                          f"推理FPS: {inference_stats.get('actual_fps', 0):.1f} (目标: {inference_stats.get('target_fps', 0):.1f})",
                          "cyan", ("Arial", 10))
