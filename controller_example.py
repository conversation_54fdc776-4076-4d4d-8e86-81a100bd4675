#!/usr/bin/env python3
"""
Simple example of using the AimController
"""

import time
from aim_controller_core import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ControllerConfig, Point


def main():
    """Simple controller usage example"""
    
    # Create controller configuration
    config = ControllerConfig(
        move_speed=0.8,
        smoothing_factor=0.3,
        prediction_enabled=True,
        prediction_time=0.05,
        max_move_distance=30.0,
        min_move_threshold=1.0
    )
    
    # Create controller instance
    controller = AimController(config)
    
    # Set initial position
    controller.set_position(Point(100, 100))
    
    print("AimController Example")
    print("====================")
    print(f"Initial position: ({controller.current_position.x:.1f}, {controller.current_position.y:.1f})")
    
    # Simulate target movement and controller response
    targets = [
        Point(200, 150, time.time()),
        Point(300, 200, time.time() + 0.1),
        Point(250, 300, time.time() + 0.2),
        Point(150, 250, time.time() + 0.3),
        Point(100, 100, time.time() + 0.4),
    ]
    
    for i, target in enumerate(targets):
        print(f"\nStep {i+1}: Target at ({target.x:.1f}, {target.y:.1f})")
        
        # Update target
        controller.update_target(target)
        
        # Simulate multiple controller updates
        for j in range(10):
            time.sleep(0.016)  # ~60fps
            aim_pos = controller.update_position()
            
            distance = ((target.x - aim_pos.x)**2 + (target.y - aim_pos.y)**2)**0.5
            print(f"  Update {j+1}: Aim at ({aim_pos.x:.1f}, {aim_pos.y:.1f}), Distance: {distance:.1f}px")
            
            if distance < 2.0:  # Close enough
                break
    
    # Show final statistics
    stats = controller.get_performance_stats()
    print(f"\nFinal Statistics:")
    print(f"Total distance moved: {stats['total_distance_moved']:.1f}px")
    print(f"Update count: {stats['update_count']}")
    print(f"Average move per update: {stats['avg_move_per_update']:.2f}px")
    
    predictor_stats = stats['predictor_stats']
    print(f"Predictor - Avg speed: {predictor_stats['avg_speed']:.1f}px/s")
    print(f"Predictor - Max speed: {predictor_stats['max_speed']:.1f}px/s")


if __name__ == "__main__":
    main()
