# FPS AI自瞄控制器核心模块

## 概述

`aim_controller_core.py` 是一个专门为FPS游戏AI自瞄设计的控制器核心模块，提供了平滑、精确的瞄准控制算法。

## 核心特性

### 1. 模块化设计
- **AimController**: 主控制器类
- **TargetPredictor**: 目标位置预测器
- **MovementSmoother**: 移动平滑器
- **ControllerConfig**: 配置参数管理

### 2. 算法特点
- **平滑移动**: 避免抖动和不自然的移动
- **目标预测**: 基于历史轨迹预测目标未来位置
- **延时补偿**: 通过预测算法补偿推理延时
- **参数可调**: 支持实时调整各种控制参数

## 使用方法

### 基本使用

```python
from aim_controller_core import AimController, ControllerConfig, Point
import time

# 创建配置
config = ControllerConfig(
    move_speed=0.8,           # 移动速度系数
    smoothing_factor=0.3,     # 平滑系数
    prediction_enabled=True,  # 启用预测
    prediction_time=0.05,     # 预测时间
    max_move_distance=30.0,   # 最大移动距离
    min_move_threshold=1.0    # 最小移动阈值
)

# 创建控制器
controller = AimController(config)

# 设置初始位置
controller.set_position(Point(400, 300))

# 更新目标位置
target = Point(500, 350, time.time())
controller.update_target(target)

# 获取新的瞄准位置
aim_position = controller.update_position()
print(f"瞄准位置: ({aim_position.x:.1f}, {aim_position.y:.1f})")
```

### 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `move_speed` | float | 0.8 | 移动速度系数 (0-1)，越大移动越快 |
| `smoothing_factor` | float | 0.3 | 平滑系数 (0-1)，越大越平滑但响应越慢 |
| `prediction_enabled` | bool | False | 是否启用目标位置预测 |
| `prediction_time` | float | 0.1 | 预测时间长度（秒） |
| `max_move_distance` | float | 50.0 | 单次最大移动距离（像素） |
| `min_move_threshold` | float | 1.0 | 最小移动阈值（像素） |

### 性能监控

```python
# 获取性能统计
stats = controller.get_performance_stats()
print(f"总移动距离: {stats['total_distance_moved']:.1f}px")
print(f"更新次数: {stats['update_count']}")
print(f"平均每次移动: {stats['avg_move_per_update']:.2f}px")

# 获取预测器信息
predictor_stats = stats['predictor_stats']
print(f"平均速度: {predictor_stats['avg_speed']:.1f}px/s")
print(f"最大速度: {predictor_stats['max_speed']:.1f}px/s")
```

## 算法原理

### 1. 目标预测算法
- 基于历史位置点计算加权平均速度
- 使用线性预测模型预测未来位置
- 新数据权重更高，提高预测准确性

### 2. 平滑移动算法
- 使用速度平滑避免突然的方向改变
- 限制单次移动距离防止跳跃
- 动态调整移动速度适应不同距离

### 3. 控制策略
- 距离阈值控制：只有距离大于阈值才移动
- 时间间隔保护：防止异常时间间隔影响计算
- 最大速度限制：避免过快移动导致的不稳定

## 测试和调优

### 1. 使用测试程序
```bash
python aim_controller_test.py
```

### 2. 参数调优建议

**高精度场景**:
- `move_speed`: 0.6-0.8
- `smoothing_factor`: 0.4-0.6
- `prediction_enabled`: True

**快速响应场景**:
- `move_speed`: 0.8-1.0
- `smoothing_factor`: 0.2-0.3
- `prediction_enabled`: False

**延时补偿场景**:
- `prediction_enabled`: True
- `prediction_time`: 0.05-0.15 (根据实际延时调整)

### 3. 性能指标
- **平均距离**: 越小表示跟踪越精确
- **移动平滑度**: 通过速度变化率评估
- **响应延时**: 从目标变化到瞄准点响应的时间

## 集成到实际项目

### 1. 替换鼠标控制
```python
import pyautogui

# 在实际应用中，将计算出的位置应用到鼠标移动
aim_pos = controller.update_position()
pyautogui.moveTo(aim_pos.x, aim_pos.y)
```

### 2. 与AI推理集成
```python
# 每次AI推理后更新目标
def on_ai_inference_result(target_x, target_y):
    target = Point(target_x, target_y, time.time())
    controller.update_target(target)
    
    # 获取控制器输出
    aim_pos = controller.update_position()
    return aim_pos.x, aim_pos.y
```

## 注意事项

1. **时间戳重要性**: 确保Point对象包含准确的时间戳
2. **更新频率**: 建议60fps或更高的更新频率
3. **参数调优**: 根据具体游戏和硬件环境调整参数
4. **边界检查**: 在实际应用中添加屏幕边界检查
5. **异常处理**: 添加适当的异常处理机制

## 扩展功能

控制器设计为可扩展的，可以轻松添加：
- 更复杂的预测算法（如卡尔曼滤波）
- 不同的平滑策略（如贝塞尔曲线）
- 自适应参数调整
- 多目标跟踪支持
