# -*- coding: utf-8 -*-
# 文件名: capture.py
# 功能: 基于 C++ DLL 的高性能屏幕截图模块和UDP视频流接收模块

# --- 导入库 ---
# 就像做菜前要准备好食材和厨具一样，编程前也要先把需要用到的工具（库）准备好。

# 导入OpenCV库，并给它取一个简称'cv2'。OpenCV是计算机视觉领域非常强大的工具，
# 我们可以用它来处理图片、视频，比如显示图片、转换颜色等。
import cv2
# 从mss库中，我们只需要导入'mss'这个工具。mss是一个专门用来截屏的库，它的优点是速度非常快。
#from mss import mss
# 导入NumPy库，并给它取一个简称'np'。NumPy是Python里用来处理数学计算，特别是数组和矩阵计算的超级工具。
# 我们截取到的屏幕画面，就会被存成NumPy数组的格式。
import numpy as np
# 从time库中，我们只需要导入'perf_counter'这个工具。它像一个秒表，可以非常精确地测量时间，
# 我们可以用它来计算我们的程序运行有多快（比如计算FPS）。
from time import perf_counter
import ctypes
from typing import Optional, Tuple # 导入兼容旧版本的类型提示

# 新增：导入用于UDP通信的socket库和线程安全的锁
import socket
import threading
import time

DLL_PATH = "./bin/Dll1.dll" #! 注意：请确保这个路径相对于您的主运行脚本是正确的

# --- 定义屏幕捕获类 ---
# '类(class)'就像一个蓝图，我们可以用它来创建很多具有相同属性和功能的对象。
# 这里我们定义一个叫做'ScreenCapture'的类，专门用来负责所有和截屏相关的工作。
class ScreenCapture:
    """
    一个高性能的屏幕截图类，通过调用底层的 C++ DLL 来实现。
    支持全屏截图和指定区域截图(ROI)。
    """

    # --- 初始化方法 ---
    # '__init__'是一个特殊的方法（也叫构造函数），当我们创建一个类的实例（对象）时，
    # 这个方法会自动被调用。我们在这里做一些初始化的设置工作。
    # 'self'代表这个类的实例本身，让你可以在类内部访问它的属性和方法。
    def __init__(self, roi: Optional[Tuple[int, int, int, int]] = None):
        """
        初始化截图器。

        参数 (Parameters):
            roi (tuple, optional): 一个四元组 (x, y, width, height) 来定义截图区域。
                                 如果为 None (默认)，则进行全屏截图。
        """
        self._lib = None
        self._handle = None
        self._width = 0
        self._height = 0
        self._buffer = None
        self._buffer_size = 0

        try:
            self._lib = ctypes.CDLL(DLL_PATH)
            self._setup_prototypes()
        except Exception as e:
            raise RuntimeError(f"加载或设置DLL失败: {e}\n请确保DLL路径 '{DLL_PATH}' 正确且文件存在。")

        self._handle = self._lib.create_capturer()
        if not self._handle:
            raise RuntimeError("创建C++截图器句柄失败。")
        
        # 配置截图模式
        display_index = 0 # 主显示器
        if roi and len(roi) == 4:
            print(f"配置为区域截图: ROI={roi}")
            use_roi = True
            roi_x, roi_y, roi_width, roi_height = roi
        else:
            print("配置为全屏截图")
            use_roi = False
            roi_x, roi_y, roi_width, roi_height = 0, 0, 0, 0
            
        config_ok = self._lib.configure_dxgi_capture(
            self._handle, display_index, use_roi, 
            roi_x, roi_y, roi_width, roi_height
        )
        if not config_ok:
            self.close()
            raise RuntimeError("配置DXGI截图模式失败。")

        # 获取输出尺寸并准备缓冲区
        w, h = ctypes.c_int(), ctypes.c_int()
        if not self._lib.get_output_dimensions(self._handle, ctypes.byref(w), ctypes.byref(h)):
            self.close()
            raise RuntimeError("获取输出尺寸失败。")
        
        self._width = w.value
        self._height = h.value
        self._buffer_size = self._width * self._height * 4
        if self._buffer_size > 0:
            self._buffer = (ctypes.c_byte * self._buffer_size)()
        
        print(f"截图器初始化成功，输出尺寸: {self._width}x{self._height}")

    def _setup_prototypes(self):
        """设置所有ctypes函数原型"""
        self._lib.create_capturer.restype = ctypes.c_void_p
        self._lib.configure_dxgi_capture.argtypes = [ctypes.c_void_p, ctypes.c_uint, ctypes.c_bool, ctypes.c_int, ctypes.c_int, ctypes.c_int, ctypes.c_int]
        self._lib.configure_dxgi_capture.restype = ctypes.c_bool
        self._lib.get_output_dimensions.argtypes = [ctypes.c_void_p, ctypes.POINTER(ctypes.c_int), ctypes.POINTER(ctypes.c_int)]
        self._lib.get_output_dimensions.restype = ctypes.c_bool
        self._lib.capture_frame.argtypes = [ctypes.c_void_p, ctypes.POINTER(ctypes.c_byte), ctypes.c_uint32]
        self._lib.capture_frame.restype = ctypes.c_long
        self._lib.release_capturer.argtypes = [ctypes.c_void_p]

    # --- 截图方法 ---
    def capture(self) -> Optional[np.ndarray]:
        """
        捕获一帧画面。

        返回 (Returns):
            np.ndarray: 一个包含BGR-A图像数据的NumPy数组。
                        如果没有新的画面，则返回 None。
        """
        if not self._handle or not self._buffer:
            return None
            
        hr = self._lib.capture_frame(self._handle, self._buffer, self._buffer_size)

        if hr == 0: # S_OK
            return np.frombuffer(self._buffer, dtype=np.uint8).reshape((self._height, self._width, 4))
        
        return None # S_FALSE or error

    def close(self):
        """释放所有C++资源"""
        if self._handle and self._lib:
            print("正在释放截图器资源...")
            self._lib.release_capturer(self._handle)
            self._handle = None
        self._lib = None
    
    # --- 支持 with 语句 ---
    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


# --- 新增: UDP MJPEG视频流接收类 ---
class UDPMjpegCapture:
    """
    从OBS通过UDP接收MJPEG视频流的捕获类。
    提供与ScreenCapture相似的接口，便于在系统中无缝替换。
    """
    
    def __init__(self, host: str = '127.0.0.1', port: int = 8000, buffer_size: int = 65536, max_buffer: int = 10*1024*1024):
        """
        初始化UDP MJPEG接收器
        
        参数:
            host: 监听地址，默认为127.0.0.1
            port: 监听端口，默认为8000
            buffer_size: UDP缓冲区大小，默认为65536字节
            max_buffer: 帧缓冲区最大大小，防止内存泄漏，默认10MB
        """
        self.host = host
        self.port = port
        self.buffer_size = buffer_size
        self.max_buffer_size = max_buffer
        self.socket = None
        self.running = False
        self.current_frame = None
        self.lock = threading.Lock()
        self.width = 0
        self.height = 0
        
        # MJPEG帧缓冲
        self.frame_buffer = bytearray()
        self.jpg_start = bytes([0xFF, 0xD8])  # JPEG起始标记
        self.jpg_end = bytes([0xFF, 0xD9])    # JPEG结束标记
        
        # 性能监控
        self.fps = 0
        self.frame_count = 0
        self.last_fps_time = time.time()
        
        # 状态追踪
        self.last_frame_time = 0
        self.frames_received = 0
        
        # 初始化接收器
        self._initialize()

    def _initialize(self):
        """初始化UDP套接字和接收线程"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.bind((self.host, self.port))
            self.socket.settimeout(1.0)  # 设置超时，使stop方法能正常工作
            
            self.running = True
            
            # 启动接收线程
            self.receiver_thread = threading.Thread(target=self._receive_loop, daemon=True)
            self.receiver_thread.start()
            
            print(f"UDP MJPEG接收器已启动，正在监听 {self.host}:{self.port}")
        except Exception as e:
            self.close()
            raise RuntimeError(f"初始化UDP接收器失败: {e}")
    
    def _receive_loop(self):
        """接收循环，在独立线程中运行"""
        while self.running:
            try:
                data, _ = self.socket.recvfrom(self.buffer_size)
                if data:
                    self._process_data(data)
                    self.frames_received += 1
            except socket.timeout:
                continue
            except Exception as e:
                if self.running:  # 只有在正常运行时才报告错误
                    print(f"接收数据出错: {e}")
    
    def _process_data(self, data):
        """处理接收到的UDP数据包"""
        with self.lock:
            # 添加数据到缓冲区，同时检查缓冲区大小
            if len(self.frame_buffer) + len(data) > self.max_buffer_size:
                # 缓冲区过大，可能是处理不及时或数据格式有问题，清空缓冲区
                print(f"警告: 缓冲区过大 ({len(self.frame_buffer)}), 正在清空")
                self.frame_buffer = bytearray()
            
            self.frame_buffer.extend(data)
            
            # 查找完整的JPEG帧
            start_pos = self.frame_buffer.find(self.jpg_start)
            if start_pos == -1:
                return  # 没找到JPEG开始标记
            
            # 从开始标记位置查找结束标记
            end_pos = self.frame_buffer.find(self.jpg_end, start_pos)
            if end_pos == -1:
                return  # 没找到JPEG结束标记
                
            # 提取完整的JPEG帧
            end_pos += 2  # 包含结束标记
            jpg_data = self.frame_buffer[start_pos:end_pos]
            
            # 解码JPEG数据
            try:
                img = cv2.imdecode(np.frombuffer(jpg_data, dtype=np.uint8), cv2.IMREAD_COLOR)
                if img is not None:
                    self.current_frame = img
                    # 更新宽高信息
                    if self.height == 0 or self.width == 0:
                        self.height, self.width = img.shape[:2]
                        print(f"UDP视频流尺寸: {self.width}x{self.height}")
                    self.last_frame_time = time.time()
                    
                    # 更新FPS
                    self.frame_count += 1
                    elapsed = time.time() - self.last_fps_time
                    if elapsed >= 1.0:  # 每秒更新一次FPS
                        self.fps = self.frame_count / elapsed
                        self.frame_count = 0
                        self.last_fps_time = time.time()
            except Exception as e:
                print(f"解码JPEG数据失败: {e}")
            
            # 清理已处理的数据
            self.frame_buffer = self.frame_buffer[end_pos:]
    
    def capture(self) -> Optional[np.ndarray]:
        """
        获取当前帧，提供与ScreenCapture相同的接口
        
        返回:
            np.ndarray: 包含BGR图像数据的NumPy数组，如果没有可用的帧则返回None
        """
        with self.lock:
            if self.current_frame is None:
                return None
            
            # 检查帧是否过期（超过1秒无更新）
            if time.time() - self.last_frame_time > 1.0:
                # 帧可能已过期，但仍然返回最后一帧
                # 可以在这里添加日志记录或者返回None
                pass
                
            return self.current_frame.copy()
    
    def get_stats(self):
        """
        获取性能统计信息
        
        返回:
            dict: 包含FPS和接收帧数等信息
        """
        return {
            "fps": self.fps,
            "frames_received": self.frames_received,
            "buffer_size": len(self.frame_buffer),
            "resolution": (self.width, self.height)
        }
    
    def close(self):
        """关闭接收器并释放资源"""
        self.running = False
        if self.receiver_thread and self.receiver_thread.is_alive():
            self.receiver_thread.join(timeout=2.0)
        if self.socket:
            self.socket.close()
            self.socket = None
        print("UDP MJPEG接收器已关闭")
    
    # --- 支持 with 语句 ---
    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


# --- 程序主入口 ---
# 这是一条判断语句，意思是：如果这个python文件是直接被运行的（而不是被其他文件导入的），
# 那么就执行下面的代码。这通常用来放一些测试代码。
if __name__ == "__main__":
    
    # 打印一条提示信息，告诉用户如何使用这个测试程序。
    print("--- 这是一个测试程序，用于显示不同捕获方式的效果 ---")
    print("--- 按下 'ESC' 键可以退出程序 ---")
    print("--- 按下 '1' 键切换到屏幕捕获 ---")
    print("--- 按下 '2' 键切换到UDP视频流接收 ---")

    # --- 使用示例 ---
    capture_mode = "screen"  # 初始模式为屏幕捕获
    screen_capturer = None
    udp_capturer = None
    
    try:
        # 准备屏幕捕获
        screen_w, screen_h = 2560, 1440  # 根据实际屏幕尺寸调整
        target_size = 640
        roi = (
            (screen_w - target_size) // 2,
            (screen_h - target_size) // 2,
            target_size,
            target_size
        )
        
        # 根据当前模式初始化对应的捕获器
        if capture_mode == "screen":
            screen_capturer = ScreenCapture(roi=roi)
        else:
            udp_capturer = UDPMjpegCapture(host='127.0.0.1', port=8000)
        
        while True:
            # 1. 根据当前模式抓取图像
            img = None
            stats_text = ""
            
            if capture_mode == "screen" and screen_capturer:
                img = screen_capturer.capture()
                stats_text = "模式: 屏幕捕获"
            elif capture_mode == "udp" and udp_capturer:
                img = udp_capturer.capture()
                stats = udp_capturer.get_stats()
                stats_text = f"模式: UDP流 | FPS: {stats['fps']:.1f} | 帧数: {stats['frames_received']}"
            
            # 2. 如果成功抓取到图像，则显示
            if img is not None:
                # 添加状态信息
                cv2.putText(img, stats_text, (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                
                cv2.imshow("Capture Test", img)
            
            # 3. 处理按键
            key = cv2.waitKey(1) & 0xFF
            if key == 27:  # ESC键退出
                break
            elif key == ord('1') and capture_mode != "screen":
                # 切换到屏幕捕获
                if udp_capturer:
                    udp_capturer.close()
                    udp_capturer = None
                
                capture_mode = "screen"
                screen_capturer = ScreenCapture(roi=roi)
                print("已切换到屏幕捕获模式")
                
            elif key == ord('2') and capture_mode != "udp":
                # 切换到UDP视频流接收
                if screen_capturer:
                    screen_capturer.close()
                    screen_capturer = None
                
                capture_mode = "udp"
                udp_capturer = UDPMjpegCapture(host='127.0.0.1', port=8000)
                print("已切换到UDP视频流接收模式")
    
    except RuntimeError as e:
        print(f"发生运行时错误: {e}")
    
    finally:
        # 确保释放所有资源
        if screen_capturer:
            screen_capturer.close()
        if udp_capturer:
            udp_capturer.close()
        cv2.destroyAllWindows()
        print("--- 测试程序结束 ---")