#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时截图推理程序
整合截图、推理、后处理三个模块，实现完整的AI自瞄流水线
"""

import cv2
import numpy as np
import time
from typing import Optional, Tuple, List, Dict
import threading
import queue

# 导入自定义模块
from capture import ScreenCapture, UDPMjpegCapture
from infer import ONNXInference
from postprocess_enhanced import EnhancedDetectionPostProcessor

# 导入控制器模块
import sys
import os
sys.path.append(r'C:\Users\<USER>\Desktop\控制器\控制器测试')
from aim_controller_core import AimController, ControllerConfig, Point

# 导入系统鼠标控制
import pyautogui


class RealtimeInferenceEngine:
    """实时推理引擎
    
    整合截图、推理、后处理功能，提供完整的AI检测流水线
    """
    
    def __init__(self,
                 model_path: str,
                 capture_mode: str = "screen",
                 roi: Optional[Tuple[int, int, int, int]] = None,
                 confidence_threshold: float = 0.5,
                 nms_threshold: float = 0.4,
                 class_names: List[str] = None,
                 udp_host: str = "127.0.0.1",
                 udp_port: int = 8000,
                 enable_aim_control: bool = True):
        """
        初始化实时推理引擎

        Args:
            model_path: ONNX模型文件路径
            capture_mode: 截图模式 ("screen" 或 "udp")
            roi: 截图区域 (x, y, width, height)，None表示全屏
            confidence_threshold: 置信度阈值
            nms_threshold: NMS阈值
            class_names: 类别名称列表
            udp_host: UDP接收地址
            udp_port: UDP接收端口
            enable_aim_control: 是否启用自瞄控制
        """
        print("=== 初始化实时推理引擎 ===")
        
        # 保存配置
        self.model_path = model_path
        self.capture_mode = capture_mode
        self.roi = roi
        self.udp_host = udp_host
        self.udp_port = udp_port
        self.enable_aim_control = enable_aim_control

        # 初始化组件
        self.capturer = None
        self.inferencer = None
        self.postprocessor = None
        self.aim_controller = None
        
        # 性能统计
        self.fps_counter = 0
        self.fps_start_time = time.time()
        self.current_fps = 0
        self.inference_times = []
        
        # 运行状态
        self.running = False
        self.frame_queue = queue.Queue(maxsize=2)  # 限制队列大小避免延迟累积

        # 控制器状态
        self.aim_enabled = False  # 自瞄开关状态
        self.screen_center_x = 0
        self.screen_center_y = 0

        # 初始化各个模块
        self._initialize_components(confidence_threshold, nms_threshold, class_names)
        
        print("=== 实时推理引擎初始化完成 ===\n")
    
    def _initialize_components(self, confidence_threshold: float, 
                             nms_threshold: float, class_names: List[str]):
        """初始化各个组件"""
        
        # 1. 初始化截图模块
        print("1. 初始化截图模块...")
        if self.capture_mode == "screen":
            self.capturer = ScreenCapture(roi=self.roi)
            print(f"   屏幕截图模式，ROI: {self.roi}")
        elif self.capture_mode == "udp":
            self.capturer = UDPMjpegCapture(host=self.udp_host, port=self.udp_port)
            print(f"   UDP视频流模式，地址: {self.udp_host}:{self.udp_port}")
        else:
            raise ValueError(f"不支持的截图模式: {self.capture_mode}")
        
        # 2. 初始化推理模块
        print("2. 初始化推理模块...")
        self.inferencer = ONNXInference(self.model_path)
        print(f"   模型输入尺寸: {self.inferencer.input_size}")
        
        # 3. 初始化后处理模块
        print("3. 初始化后处理模块...")
        self.postprocessor = EnhancedDetectionPostProcessor(
            confidence_threshold=confidence_threshold,
            nms_threshold=nms_threshold,
            class_names=class_names or ["头部", "身体"],
            model_path=self.model_path
        )

        # 4. 初始化控制器模块
        if self.enable_aim_control:
            print("4. 初始化AI自瞄控制器...")
            controller_config = ControllerConfig(
                move_speed=0.8,
                smoothing_factor=0.3,
                prediction_enabled=True,
                prediction_time=0.05,
                max_move_distance=20.0,
                min_move_threshold=0.0
            )
            self.aim_controller = AimController(controller_config)

            # 获取屏幕中心点
            if self.roi:
                self.screen_center_x = self.roi[0] + self.roi[2] // 2
                self.screen_center_y = self.roi[1] + self.roi[3] // 2
            else:
                screen_size = pyautogui.size()
                self.screen_center_x = screen_size.width // 2
                self.screen_center_y = screen_size.height // 2

            # 设置控制器初始位置（以0,0为中心，表示无偏移）
            self.aim_controller.set_position(Point(0, 0))

            # 配置pyautogui
            pyautogui.FAILSAFE = False  # 禁用安全模式
            pyautogui.PAUSE = 0  # 移除延迟

            print(f"   屏幕中心点: ({self.screen_center_x}, {self.screen_center_y})")
            print("   按 'F' 键切换自瞄开关")
        else:
            print("4. 自瞄控制器已禁用")
    
    def start(self):
        """启动实时推理"""
        if self.running:
            print("推理引擎已在运行中")
            return
        
        print("启动实时推理引擎...")
        self.running = True
        
        # 启动推理线程
        self.inference_thread = threading.Thread(target=self._inference_loop, daemon=True)
        self.inference_thread.start()
        
        # 主线程处理显示
        self._display_loop()
    
    def stop(self):
        """停止实时推理"""
        print("正在停止推理引擎...")
        self.running = False
        
        # 等待线程结束
        if hasattr(self, 'inference_thread'):
            self.inference_thread.join(timeout=2.0)
        
        # 清理资源
        if self.capturer:
            self.capturer.close()
        
        cv2.destroyAllWindows()
        print("推理引擎已停止")
    
    def _inference_loop(self):
        """推理循环（在独立线程中运行）"""
        while self.running:
            try:
                # 1. 截图
                frame = self.capturer.capture()
                if frame is None:
                    time.sleep(0.001)  # 短暂等待
                    continue
                
                # 转换颜色格式（如果需要）
                if len(frame.shape) == 3 and frame.shape[2] == 4:
                    # BGRA -> BGR
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)
                
                # 2. 推理
                inference_start = time.perf_counter()
                output = self.inferencer.inference(frame)
                inference_time = (time.perf_counter() - inference_start) * 1000
                
                # 3. 后处理
                result_img, detections = self.postprocessor.process(
                    output, frame, self.inferencer.input_size
                )
                
                # 4. 处理自瞄控制
                if self.enable_aim_control and self.aim_enabled and detections:
                    self._process_aim_control(detections)

                # 5. 添加性能信息
                self._add_performance_info(result_img, inference_time, detections)

                # 6. 将结果放入队列
                if not self.frame_queue.full():
                    self.frame_queue.put((result_img, detections))
                
                # 更新性能统计
                self.inference_times.append(inference_time)
                if len(self.inference_times) > 100:
                    self.inference_times.pop(0)
                
            except Exception as e:
                print(f"推理循环错误: {e}")
                time.sleep(0.1)
    
    def _display_loop(self):
        """显示循环（在主线程中运行）"""
        print("开始显示循环，按 'q' 或 ESC 退出...")
        
        while self.running:
            try:
                # 获取处理结果
                if not self.frame_queue.empty():
                    result_img, detections = self.frame_queue.get()
                    
                    # 显示结果
                    cv2.imshow("AI实时检测", result_img)
                    
                    # 更新FPS
                    self._update_fps()
                    
                    # 打印检测结果（可选）
                    if detections:
                        self._print_detections(detections)
                
                # 处理按键
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q') or key == 27:  # 'q' 或 ESC
                    break
                elif key == ord('s'):  # 's' 保存截图
                    self._save_screenshot()
                elif key == ord('r'):  # 'r' 重置统计
                    self._reset_stats()
                elif key == ord('f') or key == ord('F'):  # 'f' 切换自瞄开关
                    if self.enable_aim_control:
                        self.aim_enabled = not self.aim_enabled
                        status = "开启" if self.aim_enabled else "关闭"
                        print(f"自瞄控制已{status}")
                
            except Exception as e:
                print(f"显示循环错误: {e}")
                break
        
        self.stop()

    def _process_aim_control(self, detections: List[Dict]):
        """处理自瞄控制逻辑 - FPS游戏专用"""
        if not self.aim_controller or not detections:
            return

        # 选择最佳目标（优先选择头部，其次身体，按置信度排序）
        best_target = self._select_best_target(detections)
        if not best_target:
            return

        # 计算目标在ROI中的坐标
        target_roi_x, target_roi_y = self._get_target_roi_coords(best_target)

        # 计算目标相对于ROI中心的偏移
        roi_center_x = self.roi[2] // 2  # ROI宽度的一半
        roi_center_y = self.roi[3] // 2  # ROI高度的一半

        offset_x = target_roi_x - roi_center_x
        offset_y = target_roi_y - roi_center_y

        # 更新控制器目标位置（使用偏移量）
        target_point = Point(offset_x, offset_y, time.time())
        self.aim_controller.update_target(target_point)

        # 获取控制器输出的移动量
        aim_point = self.aim_controller.update_position()

        # 计算鼠标移动量（相对移动）
        move_x = aim_point.x
        move_y = aim_point.y

        # 执行相对鼠标移动
        if abs(move_x) > 0.5 or abs(move_y) > 0.5:  # 避免微小移动
            try:
                pyautogui.moveRel(int(move_x), int(move_y))
                print(f"鼠标移动: ({move_x:.1f}, {move_y:.1f})")
            except Exception as e:
                print(f"鼠标移动错误: {e}")

    def _select_best_target(self, detections: List[Dict]) -> Optional[Dict]:
        """选择最佳攻击目标"""
        if not detections:
            return None

        # 分离头部和身体检测
        heads = [d for d in detections if d.get('class_name') == '头部']
        bodies = [d for d in detections if d.get('class_name') == '身体']

        # 优先选择置信度最高的头部
        if heads:
            return max(heads, key=lambda x: x.get('confidence', 0))

        # 如果没有头部，选择置信度最高的身体
        if bodies:
            return max(bodies, key=lambda x: x.get('confidence', 0))

        # 如果都没有，选择置信度最高的检测结果
        return max(detections, key=lambda x: x.get('confidence', 0))

    def _get_target_roi_coords(self, detection: Dict) -> Tuple[float, float]:
        """获取目标在ROI中的坐标"""
        # 获取检测框中心点
        bbox = detection.get('bbox', [0, 0, 0, 0])  # [x1, y1, x2, y2]
        center_x = (bbox[0] + bbox[2]) / 2
        center_y = (bbox[1] + bbox[3]) / 2

        return center_x, center_y

    def _convert_to_screen_coords(self, detection: Dict) -> Tuple[int, int]:
        """将检测框坐标转换为屏幕绝对坐标"""
        # 获取检测框中心点
        bbox = detection.get('bbox', [0, 0, 0, 0])  # [x1, y1, x2, y2]
        center_x = (bbox[0] + bbox[2]) / 2
        center_y = (bbox[1] + bbox[3]) / 2

        # 如果有ROI，需要加上ROI的偏移
        if self.roi:
            screen_x = self.roi[0] + center_x
            screen_y = self.roi[1] + center_y
        else:
            screen_x = center_x
            screen_y = center_y

        return int(screen_x), int(screen_y)

    def _add_performance_info(self, img: np.ndarray, inference_time: float, detections: List[Dict]):
        """在图像上添加性能信息"""
        # 计算平均推理时间
        avg_inference_time = np.mean(self.inference_times) if self.inference_times else inference_time
        
        # 添加文本信息
        info_lines = [
            f"FPS: {self.current_fps:.1f}",
            f"推理时间: {inference_time:.1f}ms (平均: {avg_inference_time:.1f}ms)",
            f"检测数量: {len(detections)}",
            f"模式: {self.capture_mode.upper()}"
        ]

        # 添加自瞄状态信息
        if self.enable_aim_control:
            aim_status = "开启" if self.aim_enabled else "关闭"
            info_lines.append(f"自瞄状态: {aim_status} (按F切换)")

            if self.aim_controller:
                controller_stats = self.aim_controller.get_performance_stats()
                current_offset = controller_stats.get('current_position', (0, 0))
                info_lines.append(f"瞄准偏移: ({current_offset[0]:.1f}, {current_offset[1]:.1f})")
        
        # 绘制半透明背景
        overlay = img.copy()
        cv2.rectangle(overlay, (10, 10), (400, 10 + len(info_lines) * 25 + 10), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, img, 0.3, 0, img)
        
        # 绘制文本
        for i, line in enumerate(info_lines):
            y = 30 + i * 25
            cv2.putText(img, line, (15, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    
    def _update_fps(self):
        """更新FPS计算"""
        self.fps_counter += 1
        elapsed = time.time() - self.fps_start_time
        
        if elapsed >= 1.0:  # 每秒更新一次FPS
            self.current_fps = self.fps_counter / elapsed
            self.fps_counter = 0
            self.fps_start_time = time.time()
    
    def _print_detections(self, detections: List[Dict]):
        """打印检测结果（可选功能）"""
        # 这里可以添加检测结果的打印逻辑
        # 为了避免输出过多，可以设置条件
        pass
    
    def _save_screenshot(self):
        """保存当前截图"""
        if not self.frame_queue.empty():
            result_img, _ = self.frame_queue.queue[-1]  # 获取最新帧
            timestamp = int(time.time())
            filename = f"screenshot_{timestamp}.jpg"
            cv2.imwrite(filename, result_img)
            print(f"截图已保存: {filename}")
    
    def _reset_stats(self):
        """重置统计信息"""
        self.inference_times.clear()
        self.fps_counter = 0
        self.fps_start_time = time.time()
        print("统计信息已重置")
    
    def get_stats(self) -> Dict:
        """获取性能统计信息"""
        return {
            "current_fps": self.current_fps,
            "avg_inference_time": np.mean(self.inference_times) if self.inference_times else 0,
            "capture_mode": self.capture_mode,
            "model_path": self.model_path
        }


def main():
    """主函数"""
    print("=== AI实时自瞄程序 ===")
    print("支持的按键:")
    print("  q/ESC - 退出程序")
    print("  f/F   - 切换自瞄开关")
    print("  s     - 保存截图")
    print("  r     - 重置统计")
    print("========================")
    print("注意: 请确保游戏窗口在前台，自瞄功能需要管理员权限")
    print("========================\n")
    
    # 配置参数
    MODEL_PATH = "models/PUBGV8_320.onnx"  # 修改为你的模型路径
    CAPTURE_MODE = "screen"  # "screen" 或 "udp"
    
    # 截图区域设置（None表示全屏）
    SCREEN_W, SCREEN_H = 1920, 1080  # 根据你的屏幕分辨率调整
    TARGET_SIZE = 320
    ROI = (
        (SCREEN_W - TARGET_SIZE) // 2,
        (SCREEN_H - TARGET_SIZE) // 2,
        TARGET_SIZE,
        TARGET_SIZE
    )
    
    try:
        # 创建推理引擎
        engine = RealtimeInferenceEngine(
            model_path=MODEL_PATH,
            capture_mode=CAPTURE_MODE,
            roi=ROI,
            confidence_threshold=0.5,
            nms_threshold=0.4,
            class_names=["头部", "身体"],
            enable_aim_control=True  # 启用自瞄控制
        )
        
        # 启动推理
        engine.start()
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"程序运行错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
