#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时截图推理程序
整合截图、推理、后处理三个模块，实现完整的AI自瞄流水线
"""

import cv2
import numpy as np
import time
from typing import Optional, Tuple, List, Dict
import threading
import queue

# 导入自定义模块
from capture import ScreenCapture, UDPMjpegCapture
from infer import ONNXInference
from postprocess_enhanced import EnhancedDetectionPostProcessor


class RealtimeInferenceEngine:
    """实时推理引擎
    
    整合截图、推理、后处理功能，提供完整的AI检测流水线
    """
    
    def __init__(self, 
                 model_path: str,
                 capture_mode: str = "screen",
                 roi: Optional[Tuple[int, int, int, int]] = None,
                 confidence_threshold: float = 0.5,
                 nms_threshold: float = 0.4,
                 class_names: List[str] = None,
                 udp_host: str = "127.0.0.1",
                 udp_port: int = 8000):
        """
        初始化实时推理引擎
        
        Args:
            model_path: ONNX模型文件路径
            capture_mode: 截图模式 ("screen" 或 "udp")
            roi: 截图区域 (x, y, width, height)，None表示全屏
            confidence_threshold: 置信度阈值
            nms_threshold: NMS阈值
            class_names: 类别名称列表
            udp_host: UDP接收地址
            udp_port: UDP接收端口
        """
        print("=== 初始化实时推理引擎 ===")
        
        # 保存配置
        self.model_path = model_path
        self.capture_mode = capture_mode
        self.roi = roi
        self.udp_host = udp_host
        self.udp_port = udp_port
        
        # 初始化组件
        self.capturer = None
        self.inferencer = None
        self.postprocessor = None
        
        # 性能统计
        self.fps_counter = 0
        self.fps_start_time = time.time()
        self.current_fps = 0
        self.inference_times = []
        
        # 运行状态
        self.running = False
        self.frame_queue = queue.Queue(maxsize=2)  # 限制队列大小避免延迟累积
        
        # 初始化各个模块
        self._initialize_components(confidence_threshold, nms_threshold, class_names)
        
        print("=== 实时推理引擎初始化完成 ===\n")
    
    def _initialize_components(self, confidence_threshold: float, 
                             nms_threshold: float, class_names: List[str]):
        """初始化各个组件"""
        
        # 1. 初始化截图模块
        print("1. 初始化截图模块...")
        if self.capture_mode == "screen":
            self.capturer = ScreenCapture(roi=self.roi)
            print(f"   屏幕截图模式，ROI: {self.roi}")
        elif self.capture_mode == "udp":
            self.capturer = UDPMjpegCapture(host=self.udp_host, port=self.udp_port)
            print(f"   UDP视频流模式，地址: {self.udp_host}:{self.udp_port}")
        else:
            raise ValueError(f"不支持的截图模式: {self.capture_mode}")
        
        # 2. 初始化推理模块
        print("2. 初始化推理模块...")
        self.inferencer = ONNXInference(self.model_path)
        print(f"   模型输入尺寸: {self.inferencer.input_size}")
        
        # 3. 初始化后处理模块
        print("3. 初始化后处理模块...")
        self.postprocessor = EnhancedDetectionPostProcessor(
            confidence_threshold=confidence_threshold,
            nms_threshold=nms_threshold,
            class_names=class_names or ["头部", "身体"],
            model_path=self.model_path
        )
    
    def start(self):
        """启动实时推理"""
        if self.running:
            print("推理引擎已在运行中")
            return
        
        print("启动实时推理引擎...")
        self.running = True
        
        # 启动推理线程
        self.inference_thread = threading.Thread(target=self._inference_loop, daemon=True)
        self.inference_thread.start()
        
        # 主线程处理显示
        self._display_loop()
    
    def stop(self):
        """停止实时推理"""
        print("正在停止推理引擎...")
        self.running = False
        
        # 等待线程结束
        if hasattr(self, 'inference_thread'):
            self.inference_thread.join(timeout=2.0)
        
        # 清理资源
        if self.capturer:
            self.capturer.close()
        
        cv2.destroyAllWindows()
        print("推理引擎已停止")
    
    def _inference_loop(self):
        """推理循环（在独立线程中运行）"""
        while self.running:
            try:
                # 1. 截图
                frame = self.capturer.capture()
                if frame is None:
                    time.sleep(0.001)  # 短暂等待
                    continue
                
                # 转换颜色格式（如果需要）
                if len(frame.shape) == 3 and frame.shape[2] == 4:
                    # BGRA -> BGR
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)
                
                # 2. 推理
                inference_start = time.perf_counter()
                output = self.inferencer.inference(frame)
                inference_time = (time.perf_counter() - inference_start) * 1000
                
                # 3. 后处理
                result_img, detections = self.postprocessor.process(
                    output, frame, self.inferencer.input_size
                )
                
                # 4. 添加性能信息
                self._add_performance_info(result_img, inference_time, detections)
                
                # 5. 将结果放入队列
                if not self.frame_queue.full():
                    self.frame_queue.put((result_img, detections))
                
                # 更新性能统计
                self.inference_times.append(inference_time)
                if len(self.inference_times) > 100:
                    self.inference_times.pop(0)
                
            except Exception as e:
                print(f"推理循环错误: {e}")
                time.sleep(0.1)
    
    def _display_loop(self):
        """显示循环（在主线程中运行）"""
        print("开始显示循环，按 'q' 或 ESC 退出...")
        
        while self.running:
            try:
                # 获取处理结果
                if not self.frame_queue.empty():
                    result_img, detections = self.frame_queue.get()
                    
                    # 显示结果
                    cv2.imshow("AI实时检测", result_img)
                    
                    # 更新FPS
                    self._update_fps()
                    
                    # 打印检测结果（可选）
                    if detections:
                        self._print_detections(detections)
                
                # 处理按键
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q') or key == 27:  # 'q' 或 ESC
                    break
                elif key == ord('s'):  # 's' 保存截图
                    self._save_screenshot()
                elif key == ord('r'):  # 'r' 重置统计
                    self._reset_stats()
                
            except Exception as e:
                print(f"显示循环错误: {e}")
                break
        
        self.stop()
    
    def _add_performance_info(self, img: np.ndarray, inference_time: float, detections: List[Dict]):
        """在图像上添加性能信息"""
        # 计算平均推理时间
        avg_inference_time = np.mean(self.inference_times) if self.inference_times else inference_time
        
        # 添加文本信息
        info_lines = [
            f"FPS: {self.current_fps:.1f}",
            f"推理时间: {inference_time:.1f}ms (平均: {avg_inference_time:.1f}ms)",
            f"检测数量: {len(detections)}",
            f"模式: {self.capture_mode.upper()}"
        ]
        
        # 绘制半透明背景
        overlay = img.copy()
        cv2.rectangle(overlay, (10, 10), (400, 10 + len(info_lines) * 25 + 10), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, img, 0.3, 0, img)
        
        # 绘制文本
        for i, line in enumerate(info_lines):
            y = 30 + i * 25
            cv2.putText(img, line, (15, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    
    def _update_fps(self):
        """更新FPS计算"""
        self.fps_counter += 1
        elapsed = time.time() - self.fps_start_time
        
        if elapsed >= 1.0:  # 每秒更新一次FPS
            self.current_fps = self.fps_counter / elapsed
            self.fps_counter = 0
            self.fps_start_time = time.time()
    
    def _print_detections(self, detections: List[Dict]):
        """打印检测结果（可选功能）"""
        # 这里可以添加检测结果的打印逻辑
        # 为了避免输出过多，可以设置条件
        pass
    
    def _save_screenshot(self):
        """保存当前截图"""
        if not self.frame_queue.empty():
            result_img, _ = self.frame_queue.queue[-1]  # 获取最新帧
            timestamp = int(time.time())
            filename = f"screenshot_{timestamp}.jpg"
            cv2.imwrite(filename, result_img)
            print(f"截图已保存: {filename}")
    
    def _reset_stats(self):
        """重置统计信息"""
        self.inference_times.clear()
        self.fps_counter = 0
        self.fps_start_time = time.time()
        print("统计信息已重置")
    
    def get_stats(self) -> Dict:
        """获取性能统计信息"""
        return {
            "current_fps": self.current_fps,
            "avg_inference_time": np.mean(self.inference_times) if self.inference_times else 0,
            "capture_mode": self.capture_mode,
            "model_path": self.model_path
        }


def main():
    """主函数"""
    print("=== AI实时检测程序 ===")
    print("支持的按键:")
    print("  q/ESC - 退出程序")
    print("  s     - 保存截图")
    print("  r     - 重置统计")
    print("========================\n")
    
    # 配置参数
    MODEL_PATH = "models/1.onnx"  # 修改为你的模型路径
    CAPTURE_MODE = "screen"  # "screen" 或 "udp"
    
    # 截图区域设置（None表示全屏）
    SCREEN_W, SCREEN_H = 1920, 1080  # 根据你的屏幕分辨率调整
    TARGET_SIZE = 320
    ROI = (
        (SCREEN_W - TARGET_SIZE) // 2,
        (SCREEN_H - TARGET_SIZE) // 2,
        TARGET_SIZE,
        TARGET_SIZE
    )
    
    try:
        # 创建推理引擎
        engine = RealtimeInferenceEngine(
            model_path=MODEL_PATH,
            capture_mode=CAPTURE_MODE,
            roi=ROI,
            confidence_threshold=0.5,
            nms_threshold=0.4,
            class_names=["头部", "身体"]
        )
        
        # 启动推理
        engine.start()
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"程序运行错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
