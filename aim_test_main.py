#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FPS AI自瞄控制器测试主程序
整合UI界面和测试逻辑
"""

import threading
import time
import math
from collections import deque
import json

# 导入模块
from aim_test_ui import AimTestUI
from aim_controller_core import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ControllerConfig, Point


class InferenceSimulator:
    """AI推理模拟器 - 模拟固定频率的AI推理循环"""
    
    def __init__(self, inference_interval_ms: int = 30):
        self.inference_interval_ms = inference_interval_ms
        self.current_mouse_position = Point(400, 300)
        self.latest_inference_result = Point(400, 300)
        self.running = False
        self.thread = None
        self.inference_count = 0
        self.last_inference_time = 0
    
    def start(self):
        """启动推理循环"""
        self.running = True
        self.thread = threading.Thread(target=self._inference_loop, daemon=True)
        self.thread.start()
    
    def stop(self):
        """停止推理循环"""
        self.running = False
        if self.thread:
            self.thread.join()
    
    def update_mouse_position(self, position: Point):
        """更新当前鼠标位置"""
        self.current_mouse_position = position
    
    def get_latest_inference_result(self) -> Point:
        """获取最新的推理结果"""
        return self.latest_inference_result
    
    def get_inference_stats(self) -> dict:
        """获取推理统计信息"""
        current_time = time.time()
        elapsed_time = current_time - self.last_inference_time if self.last_inference_time > 0 else 0
        actual_fps = self.inference_count / elapsed_time if elapsed_time > 0 else 0
        
        return {
            "inference_count": self.inference_count,
            "target_fps": 1000 / self.inference_interval_ms,
            "actual_fps": actual_fps,
            "elapsed_time": elapsed_time
        }
    
    def _inference_loop(self):
        """AI推理循环 - 每隔固定时间获取一次目标位置"""
        self.last_inference_time = time.time()
        
        while self.running:
            try:
                loop_start_time = time.time()
                
                # 模拟AI推理：获取当前鼠标位置作为目标
                self.latest_inference_result = Point(
                    self.current_mouse_position.x,
                    self.current_mouse_position.y,
                    loop_start_time
                )
                
                self.inference_count += 1
                
                # 计算下次推理时间
                next_inference_time = loop_start_time + (self.inference_interval_ms / 1000.0)
                sleep_time = max(0, next_inference_time - time.time())
                
                if sleep_time > 0:
                    time.sleep(sleep_time)
                
            except Exception as e:
                print(f"推理模拟器错误: {e}")


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self, history_size: int = 100):
        self.distance_history = deque(maxlen=history_size)
        self.start_time = time.time()
    
    def add_measurement(self, target_pos: Point, aim_pos: Point):
        """添加测量数据"""
        distance = math.sqrt(
            (target_pos.x - aim_pos.x)**2 + 
            (target_pos.y - aim_pos.y)**2
        )
        self.distance_history.append(distance)
    
    def get_stats(self) -> dict:
        """获取统计数据"""
        if not self.distance_history:
            return {"avg_distance": 0, "max_distance": 0, "min_distance": 0, "sample_count": 0}
        
        distances = list(self.distance_history)
        return {
            "avg_distance": sum(distances) / len(distances),
            "max_distance": max(distances),
            "min_distance": min(distances),
            "sample_count": len(distances)
        }


class AimTestApplication:
    """AI自瞄测试应用主类"""
    
    def __init__(self):
        # 创建UI界面
        self.ui = AimTestUI()
        
        # 配置参数
        self.controller_config = ControllerConfig()
        
        # 核心组件
        self.inference_simulator = InferenceSimulator(self.ui.test_config.inference_interval_ms)
        self.aim_controller = AimController(self.controller_config)
        self.performance_analyzer = PerformanceAnalyzer()
        
        # 运行状态
        self.running = False
        self.mouse_pos = Point(400, 300)
        
        # 设置UI回调
        self.setup_ui_callbacks()
        
        # 绑定事件
        self.ui.bind_mouse_events()
        self.ui.set_close_callback(self.on_closing)
    
    def setup_ui_callbacks(self):
        """设置UI回调函数"""
        self.ui.on_mouse_move_callback = self.on_mouse_move
        self.ui.on_start_test_callback = self.start_test
        self.ui.on_stop_test_callback = self.stop_test
        self.ui.on_reset_stats_callback = self.reset_stats
        self.ui.on_save_config_callback = self.save_config
        self.ui.on_config_change_callback = self.update_config
    
    def start_test(self):
        """开始测试"""
        if not self.running:
            self.running = True
            self.update_config()
            self.inference_simulator = InferenceSimulator(self.ui.test_config.inference_interval_ms)
            self.inference_simulator.start()
            self.ui.set_button_states(start_enabled=False, stop_enabled=True)
            self.update_loop()
    
    def stop_test(self):
        """停止测试"""
        if self.running:
            self.running = False
            self.inference_simulator.stop()
            self.ui.set_button_states(start_enabled=True, stop_enabled=False)
    
    def reset_stats(self):
        """重置统计数据"""
        self.performance_analyzer = PerformanceAnalyzer()
        self.aim_controller.reset_stats()
    
    def save_config(self):
        """保存配置到文件"""
        self.update_config()
        config_values = self.ui.get_config_values()
        config_dict = {
            "inference_interval_ms": config_values["inference_interval_ms"],
            "move_speed": config_values["move_speed"],
            "smoothing_factor": config_values["smoothing_factor"],
            "prediction_enabled": config_values["prediction_enabled"],
            "prediction_time": self.controller_config.prediction_time
        }
        try:
            with open("aim_config.json", "w", encoding="utf-8") as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            print("配置已保存到 aim_config.json")
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def update_config(self):
        """更新配置参数"""
        config_values = self.ui.get_config_values()
        
        # 更新控制器配置
        self.controller_config.move_speed = config_values["move_speed"]
        self.controller_config.smoothing_factor = config_values["smoothing_factor"]
        self.controller_config.prediction_enabled = config_values["prediction_enabled"]
        
        # 更新控制器配置
        self.aim_controller.config = self.controller_config
    
    def on_mouse_move(self, event):
        """鼠标移动事件处理"""
        self.mouse_pos = Point(event.x, event.y, time.time())
        if self.running:
            self.inference_simulator.update_mouse_position(self.mouse_pos)
    
    def update_loop(self):
        """主更新循环"""
        if not self.running:
            return
        
        try:
            # 获取最新的AI推理结果
            inference_result = self.inference_simulator.get_latest_inference_result()
            self.aim_controller.update_target(inference_result)
            
            # 更新AI瞄准点位置
            aim_pos = self.aim_controller.update_position()
            
            # 更新性能统计
            self.performance_analyzer.add_measurement(self.mouse_pos, aim_pos)
            
            # 更新显示
            self.update_display(self.mouse_pos, aim_pos, inference_result)
            self.update_stats_display()
            
        except Exception as e:
            print(f"更新循环错误: {e}")
        
        # 继续循环
        self.ui.after(16, self.update_loop)  # ~60fps
    
    def update_display(self, target_pos: Point, aim_pos: Point, inference_pos: Point):
        """更新显示"""
        self.ui.clear_canvas()
        
        # 绘制网格
        self.ui.draw_grid()
        
        # 绘制目标点（绿色，跟随鼠标）
        self.ui.draw_point(target_pos.x, target_pos.y, "green", 8, "实时目标")
        
        # 绘制AI推理结果点（黄色，每30ms更新一次的位置）
        self.ui.draw_point(inference_pos.x, inference_pos.y, "yellow", 6, "AI推理")
        
        # 绘制AI瞄准点（红色）
        self.ui.draw_point(aim_pos.x, aim_pos.y, "red", 8, "AI瞄准")
        
        # 绘制连接线
        self.ui.draw_line(inference_pos.x, inference_pos.y, 
                         aim_pos.x, aim_pos.y, "cyan", 1, (5, 5))
        
        # 显示距离
        distance = math.sqrt((target_pos.x - aim_pos.x)**2 + 
                           (target_pos.y - aim_pos.y)**2)
        self.ui.draw_text(10, 10, f"实时距离: {distance:.1f}px")
        
        # 显示推理统计
        if hasattr(self, 'inference_simulator'):
            stats = self.inference_simulator.get_inference_stats()
            self.ui.draw_text(10, 30, 
                             f"推理FPS: {stats['actual_fps']:.1f} (目标: {stats['target_fps']:.1f})", 
                             "cyan", ("Arial", 10))
    
    def update_stats_display(self):
        """更新统计信息显示"""
        stats = self.performance_analyzer.get_stats()
        
        # 获取推理统计
        inference_stats = self.inference_simulator.get_inference_stats()
        
        # 获取控制器性能统计
        controller_stats = self.aim_controller.get_performance_stats()
        predictor_stats = controller_stats.get('predictor_stats', {})
        
        stats_text = f"""═══ 跟踪精度 ═══
平均距离: {stats['avg_distance']:.2f}px
最大距离: {stats['max_distance']:.2f}px  
最小距离: {stats['min_distance']:.2f}px
样本数量: {stats['sample_count']}

═══ 推理性能 ═══
目标FPS: {inference_stats['target_fps']:.1f}
实际FPS: {inference_stats['actual_fps']:.1f}
推理间隔: {self.ui.test_config.inference_interval_ms}ms

═══ 控制器状态 ═══
总移动距离: {controller_stats.get('total_distance_moved', 0):.1f}px
更新次数: {controller_stats.get('update_count', 0)}
平均移动: {controller_stats.get('avg_move_per_update', 0):.2f}px

═══ 预测器状态 ═══
平均速度: {predictor_stats.get('avg_speed', 0):.1f}px/s
最大速度: {predictor_stats.get('max_speed', 0):.1f}px/s
运动方向: {predictor_stats.get('direction', 0):.1f}°
样本数: {predictor_stats.get('sample_count', 0)}

═══ 当前配置 ═══
移动速度: {self.controller_config.move_speed:.2f}
平滑系数: {self.controller_config.smoothing_factor:.2f}
预测状态: {'开启' if self.controller_config.prediction_enabled else '关闭'}
预测时间: {self.controller_config.prediction_time:.3f}s
"""
        
        self.ui.update_stats_display(stats_text)
    
    def on_closing(self):
        """窗口关闭事件"""
        self.stop_test()
        self.ui.destroy()
    
    def run(self):
        """运行程序"""
        print("FPS AI自瞄控制器测试程序启动")
        print("使用说明:")
        print("1. 在黑色区域移动鼠标（绿点=实时目标）")
        print("2. 点击'开始测试'启动AI推理循环")
        print("3. 黄点=AI推理结果（每30ms更新一次）")
        print("4. 红点=AI瞄准点（基于推理结果移动）")
        print("5. 调整推理间隔观察不同频率的效果")
        print("6. 查看统计信息评估跟踪精度")
        
        self.ui.run()


def main():
    """主函数"""
    try:
        app = AimTestApplication()
        app.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
