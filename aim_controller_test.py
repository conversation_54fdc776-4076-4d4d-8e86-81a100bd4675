#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FPS游戏AI自瞄控制器测试程序
用于验证控制器在延时环境下的跟踪效果
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import math
import queue
from collections import deque
from dataclasses import dataclass
# from typing import Tuple, Optional  # 暂时不需要
import json


@dataclass
class Point:
    """坐标点数据类"""
    x: float
    y: float
    timestamp: float = 0.0


@dataclass
class ControllerConfig:
    """控制器配置参数"""
    delay_ms: int = 150  # 延时毫秒数
    move_speed: float = 0.8  # 移动速度系数 (0-1)
    smoothing_factor: float = 0.3  # 平滑系数 (0-1)
    prediction_enabled: bool = True  # 是否启用预测
    prediction_time: float = 0.1  # 预测时间(秒)


class DelaySimulator:
    """延时模拟器 - 模拟截图推理的延时"""
    
    def __init__(self, delay_ms: int = 150):
        self.delay_ms = delay_ms
        self.position_queue = queue.Queue()
        self.latest_delayed_position = Point(0, 0)
        self.running = False
        self.thread = None
    
    def start(self):
        """启动延时模拟"""
        self.running = True
        self.thread = threading.Thread(target=self._delay_loop, daemon=True)
        self.thread.start()
    
    def stop(self):
        """停止延时模拟"""
        self.running = False
        if self.thread:
            self.thread.join()
    
    def add_position(self, position: Point):
        """添加新的位置数据"""
        position.timestamp = time.time()
        self.position_queue.put(position)
    
    def get_delayed_position(self) -> Point:
        """获取延时后的位置"""
        return self.latest_delayed_position
    
    def _delay_loop(self):
        """延时处理循环"""
        while self.running:
            try:
                # 等待延时时间
                time.sleep(self.delay_ms / 1000.0)
                
                # 获取延时前的位置
                if not self.position_queue.empty():
                    self.latest_delayed_position = self.position_queue.get()
                
            except Exception as e:
                print(f"延时模拟器错误: {e}")


class TargetPredictor:
    """目标位置预测器"""
    
    def __init__(self, history_size: int = 5):
        self.position_history = deque(maxlen=history_size)
    
    def add_position(self, position: Point):
        """添加位置历史"""
        self.position_history.append(position)
    
    def predict_position(self, prediction_time: float) -> Point:
        """预测未来位置"""
        if len(self.position_history) < 2:
            return self.position_history[-1] if self.position_history else Point(0, 0)
        
        # 计算平均速度
        recent_positions = list(self.position_history)[-3:]  # 使用最近3个位置
        if len(recent_positions) < 2:
            return recent_positions[-1]
        
        # 计算速度矢量
        total_vx = 0
        total_vy = 0
        time_intervals = 0
        
        for i in range(1, len(recent_positions)):
            dt = recent_positions[i].timestamp - recent_positions[i-1].timestamp
            if dt > 0:
                vx = (recent_positions[i].x - recent_positions[i-1].x) / dt
                vy = (recent_positions[i].y - recent_positions[i-1].y) / dt
                total_vx += vx
                total_vy += vy
                time_intervals += 1
        
        if time_intervals == 0:
            return recent_positions[-1]
        
        # 平均速度
        avg_vx = total_vx / time_intervals
        avg_vy = total_vy / time_intervals
        
        # 预测位置
        last_pos = recent_positions[-1]
        predicted_x = last_pos.x + avg_vx * prediction_time
        predicted_y = last_pos.y + avg_vy * prediction_time
        
        return Point(predicted_x, predicted_y)


class AimController:
    """AI瞄准控制器"""
    
    def __init__(self, config: ControllerConfig):
        self.config = config
        self.current_position = Point(400, 300)  # 初始位置
        self.target_position = Point(400, 300)
        self.predictor = TargetPredictor()
        self.last_update_time = time.time()
    
    def update_target(self, target_pos: Point):
        """更新目标位置"""
        self.target_position = target_pos
        self.predictor.add_position(target_pos)
    
    def update_position(self) -> Point:
        """更新AI瞄准点位置"""
        current_time = time.time()
        dt = current_time - self.last_update_time
        self.last_update_time = current_time
        
        # 获取目标位置（可能包含预测）
        if self.config.prediction_enabled:
            target = self.predictor.predict_position(self.config.prediction_time)
        else:
            target = self.target_position
        
        # 计算移动矢量
        dx = target.x - self.current_position.x
        dy = target.y - self.current_position.y
        distance = math.sqrt(dx*dx + dy*dy)
        
        if distance > 1.0:  # 避免抖动
            # 应用平滑移动
            move_distance = distance * self.config.move_speed * dt * 60  # 60fps基准
            
            # 限制最大移动距离
            max_move = distance * self.config.smoothing_factor
            move_distance = min(move_distance, max_move)
            
            # 计算移动方向
            if distance > 0:
                move_x = (dx / distance) * move_distance
                move_y = (dy / distance) * move_distance
                
                self.current_position.x += move_x
                self.current_position.y += move_y
        
        return self.current_position


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self, history_size: int = 100):
        self.distance_history = deque(maxlen=history_size)
        self.start_time = time.time()
    
    def add_measurement(self, target_pos: Point, aim_pos: Point):
        """添加测量数据"""
        distance = math.sqrt(
            (target_pos.x - aim_pos.x)**2 + 
            (target_pos.y - aim_pos.y)**2
        )
        self.distance_history.append(distance)
    
    def get_stats(self) -> dict:
        """获取统计数据"""
        if not self.distance_history:
            return {"avg_distance": 0, "max_distance": 0, "min_distance": 0}
        
        distances = list(self.distance_history)
        return {
            "avg_distance": sum(distances) / len(distances),
            "max_distance": max(distances),
            "min_distance": min(distances),
            "sample_count": len(distances)
        }


class AimTestWindow:
    """主测试窗口"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("FPS AI自瞄控制器测试程序")
        self.root.geometry("1000x700")
        
        # 配置参数
        self.config = ControllerConfig()
        
        # 核心组件
        self.delay_simulator = DelaySimulator(self.config.delay_ms)
        self.aim_controller = AimController(self.config)
        self.performance_analyzer = PerformanceAnalyzer()
        
        # 界面组件
        self.setup_ui()
        
        # 运行状态
        self.running = False
        self.mouse_pos = Point(400, 300)
        
        # 绑定事件
        self.canvas.bind("<Motion>", self.on_mouse_move)
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 画布区域
        canvas_frame = ttk.LabelFrame(main_frame, text="测试区域")
        canvas_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        self.canvas = tk.Canvas(canvas_frame, bg="black", width=600, height=500)
        self.canvas.pack(padx=10, pady=10)
        
        # 控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板")
        control_frame.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 参数调节
        self.setup_parameter_controls(control_frame)
        
        # 统计信息
        self.setup_stats_display(control_frame)
        
        # 控制按钮
        self.setup_control_buttons(control_frame)
    
    def setup_parameter_controls(self, parent):
        """设置参数控制界面"""
        params_frame = ttk.LabelFrame(parent, text="参数设置")
        params_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 延时设置
        ttk.Label(params_frame, text="延时(ms):").pack(anchor=tk.W)
        self.delay_var = tk.IntVar(value=self.config.delay_ms)
        delay_scale = ttk.Scale(params_frame, from_=50, to=500, 
                               variable=self.delay_var, orient=tk.HORIZONTAL)
        delay_scale.pack(fill=tk.X, padx=5)
        
        # 移动速度
        ttk.Label(params_frame, text="移动速度:").pack(anchor=tk.W)
        self.speed_var = tk.DoubleVar(value=self.config.move_speed)
        speed_scale = ttk.Scale(params_frame, from_=0.1, to=2.0, 
                               variable=self.speed_var, orient=tk.HORIZONTAL)
        speed_scale.pack(fill=tk.X, padx=5)
        
        # 平滑系数
        ttk.Label(params_frame, text="平滑系数:").pack(anchor=tk.W)
        self.smooth_var = tk.DoubleVar(value=self.config.smoothing_factor)
        smooth_scale = ttk.Scale(params_frame, from_=0.1, to=1.0, 
                                variable=self.smooth_var, orient=tk.HORIZONTAL)
        smooth_scale.pack(fill=tk.X, padx=5)
        
        # 预测开关
        self.prediction_var = tk.BooleanVar(value=self.config.prediction_enabled)
        ttk.Checkbutton(params_frame, text="启用预测", 
                       variable=self.prediction_var).pack(anchor=tk.W, pady=5)
    
    def setup_stats_display(self, parent):
        """设置统计信息显示"""
        stats_frame = ttk.LabelFrame(parent, text="实时统计")
        stats_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.stats_text = tk.Text(stats_frame, height=8, width=25)
        self.stats_text.pack(padx=5, pady=5)
    
    def setup_control_buttons(self, parent):
        """设置控制按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, padx=5, pady=10)
        
        self.start_button = ttk.Button(button_frame, text="开始测试", 
                                      command=self.start_test)
        self.start_button.pack(fill=tk.X, pady=2)
        
        self.stop_button = ttk.Button(button_frame, text="停止测试", 
                                     command=self.stop_test, state=tk.DISABLED)
        self.stop_button.pack(fill=tk.X, pady=2)
        
        ttk.Button(button_frame, text="重置统计", 
                  command=self.reset_stats).pack(fill=tk.X, pady=2)
        
        ttk.Button(button_frame, text="保存配置",
                  command=self.save_config).pack(fill=tk.X, pady=2)

    def start_test(self):
        """开始测试"""
        if not self.running:
            self.running = True
            self.update_config()
            self.delay_simulator = DelaySimulator(self.config.delay_ms)
            self.delay_simulator.start()
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.update_loop()

    def stop_test(self):
        """停止测试"""
        if self.running:
            self.running = False
            self.delay_simulator.stop()
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)

    def reset_stats(self):
        """重置统计数据"""
        self.performance_analyzer = PerformanceAnalyzer()

    def save_config(self):
        """保存配置到文件"""
        self.update_config()
        config_dict = {
            "delay_ms": self.config.delay_ms,
            "move_speed": self.config.move_speed,
            "smoothing_factor": self.config.smoothing_factor,
            "prediction_enabled": self.config.prediction_enabled,
            "prediction_time": self.config.prediction_time
        }
        try:
            with open("aim_config.json", "w", encoding="utf-8") as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            print("配置已保存到 aim_config.json")
        except Exception as e:
            print(f"保存配置失败: {e}")

    def update_config(self):
        """更新配置参数"""
        self.config.delay_ms = self.delay_var.get()
        self.config.move_speed = self.speed_var.get()
        self.config.smoothing_factor = self.smooth_var.get()
        self.config.prediction_enabled = self.prediction_var.get()

        # 更新控制器配置
        self.aim_controller.config = self.config

    def on_mouse_move(self, event):
        """鼠标移动事件处理"""
        self.mouse_pos = Point(event.x, event.y, time.time())
        if self.running:
            self.delay_simulator.add_position(self.mouse_pos)

    def update_loop(self):
        """主更新循环"""
        if not self.running:
            return

        try:
            # 获取延时后的目标位置
            delayed_target = self.delay_simulator.get_delayed_position()
            self.aim_controller.update_target(delayed_target)

            # 更新AI瞄准点位置
            aim_pos = self.aim_controller.update_position()

            # 更新性能统计
            self.performance_analyzer.add_measurement(self.mouse_pos, aim_pos)

            # 更新显示
            self.update_display(self.mouse_pos, aim_pos, delayed_target)
            self.update_stats_display()

        except Exception as e:
            print(f"更新循环错误: {e}")

        # 继续循环
        self.root.after(16, self.update_loop)  # ~60fps

    def update_display(self, target_pos: Point, aim_pos: Point, delayed_pos: Point):
        """更新显示"""
        self.canvas.delete("all")

        # 绘制网格
        self.draw_grid()

        # 绘制目标点（绿色，跟随鼠标）
        self.canvas.create_oval(target_pos.x-8, target_pos.y-8,
                               target_pos.x+8, target_pos.y+8,
                               fill="green", outline="white", width=2)
        self.canvas.create_text(target_pos.x, target_pos.y-20,
                               text="目标", fill="green", font=("Arial", 10))

        # 绘制延时目标点（黄色，延时后的位置）
        self.canvas.create_oval(delayed_pos.x-6, delayed_pos.y-6,
                               delayed_pos.x+6, delayed_pos.y+6,
                               fill="yellow", outline="white", width=1)
        self.canvas.create_text(delayed_pos.x, delayed_pos.y-15,
                               text="延时", fill="yellow", font=("Arial", 8))

        # 绘制AI瞄准点（红色）
        self.canvas.create_oval(aim_pos.x-8, aim_pos.y-8,
                               aim_pos.x+8, aim_pos.y+8,
                               fill="red", outline="white", width=2)
        self.canvas.create_text(aim_pos.x, aim_pos.y-20,
                               text="AI瞄准", fill="red", font=("Arial", 10))

        # 绘制连接线
        self.canvas.create_line(delayed_pos.x, delayed_pos.y,
                               aim_pos.x, aim_pos.y,
                               fill="cyan", width=1, dash=(5, 5))

        # 显示距离
        distance = math.sqrt((target_pos.x - aim_pos.x)**2 +
                           (target_pos.y - aim_pos.y)**2)
        self.canvas.create_text(10, 10, anchor=tk.NW,
                               text=f"实时距离: {distance:.1f}px",
                               fill="white", font=("Arial", 12))

    def draw_grid(self):
        """绘制网格"""
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        # 绘制网格线
        for i in range(0, canvas_width, 50):
            self.canvas.create_line(i, 0, i, canvas_height,
                                   fill="gray20", width=1)
        for i in range(0, canvas_height, 50):
            self.canvas.create_line(0, i, canvas_width, i,
                                   fill="gray20", width=1)

    def update_stats_display(self):
        """更新统计信息显示"""
        stats = self.performance_analyzer.get_stats()

        stats_text = f"""实时统计信息:

平均距离: {stats['avg_distance']:.2f}px
最大距离: {stats['max_distance']:.2f}px
最小距离: {stats['min_distance']:.2f}px
样本数量: {stats['sample_count']}

当前配置:
延时: {self.config.delay_ms}ms
移动速度: {self.config.move_speed:.2f}
平滑系数: {self.config.smoothing_factor:.2f}
预测: {'开启' if self.config.prediction_enabled else '关闭'}
"""

        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text)

    def on_closing(self):
        """窗口关闭事件"""
        self.stop_test()
        self.root.destroy()

    def run(self):
        """运行程序"""
        print("FPS AI自瞄控制器测试程序启动")
        print("使用说明:")
        print("1. 在黑色区域移动鼠标（绿点跟随鼠标）")
        print("2. 点击'开始测试'启动AI控制器")
        print("3. 红点会尝试跟踪延时后的绿点位置")
        print("4. 调整右侧参数观察效果变化")
        print("5. 查看统计信息评估跟踪精度")

        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = AimTestWindow()
        app.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
