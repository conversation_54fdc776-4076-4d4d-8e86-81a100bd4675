# FPS AI自瞄控制器测试程序

这是一个用于验证FPS游戏AI自瞄控制器效果的测试程序。

## 功能特性

### 核心功能
- **可视化测试环境**: 使用tkinter创建测试窗口
- **双点显示系统**: 
  - 绿点：真实目标，跟随鼠标实时移动
  - 红点：AI瞄准点，通过控制器算法移动
- **延时模拟**: 模拟真实游戏中截图推理的延时（100-500ms可调）
- **平滑移动算法**: 避免瞬移，实现自然的瞄准移动
- **目标预测**: 基于历史轨迹预测目标未来位置

### 参数调节
- **延时设置**: 50-500ms，模拟不同的推理延时
- **移动速度**: 0.1-2.0，控制AI瞄准点的移动速度
- **平滑系数**: 0.1-1.0，控制移动的平滑程度
- **预测开关**: 启用/关闭目标位置预测

### 效果评估
- **实时距离显示**: 显示红绿两点间的实时距离
- **统计数据**: 平均距离、最大距离、最小距离
- **参数保存**: 可保存测试参数到配置文件

## 使用方法

### 1. 运行程序
```bash
python run_test.py
```

### 2. 操作步骤
1. 程序启动后会显示测试窗口
2. 在黑色测试区域移动鼠标（绿点会跟随）
3. 点击"开始测试"启动AI控制器
4. 红点会尝试跟踪延时后的绿点位置
5. 调整右侧参数观察效果变化
6. 查看统计信息评估跟踪精度

### 3. 参数调优建议
- **延时较小时**: 可以使用较高的移动速度和较小的平滑系数
- **延时较大时**: 建议启用预测功能，适当降低移动速度
- **目标移动较快时**: 增加预测时间，提高移动速度
- **追求稳定性时**: 增加平滑系数，降低移动速度

## 文件说明

- `aim_controller_test.py`: 主程序文件
- `run_test.py`: 启动脚本
- `aim_config.json`: 配置文件（运行后自动生成）
- `README.md`: 说明文档

## 技术架构

### 核心组件
1. **DelaySimulator**: 延时模拟器，模拟截图推理延时
2. **TargetPredictor**: 目标预测器，基于历史轨迹预测未来位置
3. **AimController**: AI瞄准控制器，核心算法实现
4. **PerformanceAnalyzer**: 性能分析器，统计跟踪精度
5. **AimTestWindow**: 可视化界面，参数调节和效果展示

### 算法特点
- **平滑移动**: 使用速度限制和平滑系数避免抖动
- **预测算法**: 基于最近3个位置点计算平均速度进行预测
- **延时补偿**: 通过队列机制模拟真实的延时环境
- **实时统计**: 持续监控跟踪精度，提供优化依据

## 预期效果

通过这个测试程序，你可以：
1. **验证控制器算法**: 在可控环境下测试不同参数的效果
2. **优化参数配置**: 找到最适合的移动速度、平滑系数等参数
3. **评估延时影响**: 了解不同延时对跟踪精度的影响
4. **测试预测算法**: 验证目标预测对跟踪效果的改善

这为实际游戏应用提供了可靠的参数调优基础。

## 注意事项

- 程序仅用于算法验证，不涉及实际游戏操作
- 建议在不同的鼠标移动模式下测试（匀速、变速、随机等）
- 可以通过修改代码添加更多的预测算法和移动策略
- 统计数据可以导出用于进一步分析
