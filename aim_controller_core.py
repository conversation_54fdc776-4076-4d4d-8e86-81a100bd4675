import time
import math
from collections import deque
from dataclasses import dataclass
from typing import List, Optional


@dataclass
class Point:
    """Coordinate point data class"""
    x: float
    y: float
    timestamp: float = 0.0


@dataclass
class ControllerConfig:
    """Controller configuration parameters"""
    move_speed: float = 0.8  # Movement speed factor (0-1)
    smoothing_factor: float = 0.3  # Smoothing factor (0-1)
    prediction_enabled: bool = False  # Enable prediction
    prediction_time: float = 0.1  # Prediction time (seconds)
    max_move_distance: float = 50.0  # Max single move distance (pixels)
    min_move_threshold: float = 1.0  # Min move threshold (pixels)


class TargetPredictor:
    """Target position predictor
    
    Predicts future target position based on historical trajectory
    """
    
    def __init__(self, history_size: int = 5):
        self.position_history = deque(maxlen=history_size)
        self.velocity_history = deque(maxlen=history_size-1)
    
    def add_position(self, position: Point):
        """Add position to history"""
        if len(self.position_history) > 0:
            # Calculate velocity
            last_pos = self.position_history[-1]
            dt = position.timestamp - last_pos.timestamp
            if dt > 0:
                vx = (position.x - last_pos.x) / dt
                vy = (position.y - last_pos.y) / dt
                self.velocity_history.append((vx, vy, dt))
        
        self.position_history.append(position)
    
    def predict_position(self, prediction_time: float) -> Point:
        """Predict future position"""
        if len(self.position_history) == 0:
            return Point(0, 0)
        
        if len(self.position_history) < 2 or not self.velocity_history:
            return self.position_history[-1]
        
        # Use weighted average to calculate velocity
        total_weight = 0
        weighted_vx = 0
        weighted_vy = 0
        
        for i, (vx, vy, dt) in enumerate(self.velocity_history):
            # Newer data has higher weight
            weight = (i + 1) / len(self.velocity_history)
            weighted_vx += vx * weight
            weighted_vy += vy * weight
            total_weight += weight
        
        if total_weight > 0:
            avg_vx = weighted_vx / total_weight
            avg_vy = weighted_vy / total_weight
        else:
            avg_vx = avg_vy = 0
        
        # Predict position
        last_pos = self.position_history[-1]
        predicted_x = last_pos.x + avg_vx * prediction_time
        predicted_y = last_pos.y + avg_vy * prediction_time
        
        return Point(predicted_x, predicted_y, last_pos.timestamp + prediction_time)
    
    def get_velocity_info(self) -> dict:
        """Get velocity info for debugging"""
        if not self.velocity_history:
            return {"avg_speed": 0, "max_speed": 0, "direction": 0}
        
        speeds = []
        for vx, vy, dt in self.velocity_history:
            speed = math.sqrt(vx*vx + vy*vy)
            speeds.append(speed)
        
        avg_speed = sum(speeds) / len(speeds)
        max_speed = max(speeds)
        
        # Latest direction
        if self.velocity_history:
            vx, vy, _ = self.velocity_history[-1]
            direction = math.atan2(vy, vx) * 180 / math.pi
        else:
            direction = 0
        
        return {
            "avg_speed": avg_speed,
            "max_speed": max_speed,
            "direction": direction,
            "sample_count": len(self.velocity_history)
        }


class MovementSmoother:
    """Movement smoother
    
    Implements smooth mouse movement to avoid jitter
    """
    
    def __init__(self, smoothing_factor: float = 0.3):
        self.smoothing_factor = smoothing_factor
        self.last_velocity = Point(0, 0)
    
    def smooth_movement(self, current_pos: Point, target_pos: Point, 
                       max_speed: float, dt: float) -> Point:
        """Calculate smooth movement to new position"""
        # Calculate target direction and distance
        dx = target_pos.x - current_pos.x
        dy = target_pos.y - current_pos.y
        distance = math.sqrt(dx*dx + dy*dy)
        
        if distance < 0.1:  # Avoid division by zero
            return current_pos
        
        # Calculate ideal velocity
        ideal_vx = (dx / distance) * max_speed
        ideal_vy = (dy / distance) * max_speed
        
        # Apply smoothing
        smooth_vx = self.last_velocity.x * self.smoothing_factor + ideal_vx * (1 - self.smoothing_factor)
        smooth_vy = self.last_velocity.y * self.smoothing_factor + ideal_vy * (1 - self.smoothing_factor)
        
        # Update velocity history
        self.last_velocity.x = smooth_vx
        self.last_velocity.y = smooth_vy
        
        # Calculate new position
        move_x = smooth_vx * dt
        move_y = smooth_vy * dt
        
        # Limit single move distance
        move_distance = math.sqrt(move_x*move_x + move_y*move_y)
        max_move = min(distance, max_speed * dt)
        
        if move_distance > max_move:
            scale = max_move / move_distance
            move_x *= scale
            move_y *= scale
        
        return Point(current_pos.x + move_x, current_pos.y + move_y)


class AimController:
    """AI Aim Controller
    
    Core controller class for smooth aiming movement
    """
    
    def __init__(self, config: ControllerConfig):
        self.config = config
        self.current_position = Point(400, 300)  # Current aim position
        self.target_position = Point(400, 300)   # Target position
        
        # Sub-components
        self.predictor = TargetPredictor()
        self.smoother = MovementSmoother(config.smoothing_factor)
        
        # State tracking
        self.last_update_time = time.time()
        self.total_distance_moved = 0.0
        self.update_count = 0
    
    def update_target(self, target_pos: Point):
        """Update target position"""
        self.target_position = target_pos
        self.predictor.add_position(target_pos)
    
    def update_position(self) -> Point:
        """Update AI aim point position"""
        current_time = time.time()
        dt = current_time - self.last_update_time
        self.last_update_time = current_time
        
        # Prevent abnormal time intervals
        if dt > 0.1:  # Over 100ms considered abnormal
            dt = 0.016  # Use 60fps standard interval
        
        # Get target position (may include prediction)
        if self.config.prediction_enabled:
            effective_target = self.predictor.predict_position(self.config.prediction_time)
        else:
            effective_target = self.target_position
        
        # Calculate distance to target
        distance_to_target = math.sqrt(
            (effective_target.x - self.current_position.x)**2 + 
            (effective_target.y - self.current_position.y)**2
        )
        
        # Only move if distance is above threshold
        if distance_to_target > self.config.min_move_threshold:
            # Calculate max movement speed
            max_speed = self.config.max_move_distance * self.config.move_speed / dt
            
            # Use smoother to calculate new position
            new_position = self.smoother.smooth_movement(
                self.current_position, effective_target, max_speed, dt
            )
            
            # Update statistics
            move_distance = math.sqrt(
                (new_position.x - self.current_position.x)**2 + 
                (new_position.y - self.current_position.y)**2
            )
            self.total_distance_moved += move_distance
            
            self.current_position = new_position
        
        self.update_count += 1
        return self.current_position
    
    def get_performance_stats(self) -> dict:
        """Get performance statistics"""
        return {
            "current_position": (self.current_position.x, self.current_position.y),
            "target_position": (self.target_position.x, self.target_position.y),
            "total_distance_moved": self.total_distance_moved,
            "update_count": self.update_count,
            "avg_move_per_update": self.total_distance_moved / max(1, self.update_count),
            "predictor_stats": self.predictor.get_velocity_info()
        }
    
    def reset_stats(self):
        """Reset statistics"""
        self.total_distance_moved = 0.0
        self.update_count = 0
        self.last_update_time = time.time()
    
    def set_position(self, position: Point):
        """Set current position directly"""
        self.current_position = position
