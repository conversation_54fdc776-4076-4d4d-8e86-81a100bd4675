import time
import math
from collections import deque
from dataclasses import dataclass
from typing import List, Optional


@dataclass
class Point:
    """坐标点数据类"""
    x: float
    y: float
    timestamp: float = 0.0


@dataclass
class ControllerConfig:
    """控制器配置参数"""
    move_speed: float = 0.8  # 移动速度系数 (0-1)
    smoothing_factor: float = 0.3  # 平滑系数 (0-1)
    prediction_enabled: bool = False  # 是否启用预测
    prediction_time: float = 0.1  # 预测时间(秒)
    max_move_distance: float = 50.0  # 单次最大移动距离(像素)
    min_move_threshold: float = 1.0  # 最小移动阈值(像素)


class TargetPredictor:
    """目标位置预测器

    核心原理：基于历史轨迹预测目标未来位置，用于补偿AI推理延时

    算法思路：
    1. 维护目标位置的历史记录
    2. 计算历史速度矢量（包含方向和大小）
    3. 使用加权平均计算当前速度趋势
    4. 基于速度趋势线性预测未来位置

    关键优化：
    - 新数据权重更高，提高对速度变化的敏感性
    - 时间戳精确计算，确保速度计算准确
    - 异常数据过滤，避免错误的速度计算
    """

    def __init__(self, history_size: int = 5):
        """
        初始化预测器

        Args:
            history_size: 保存的历史位置数量，影响预测的稳定性和响应性
                         - 数量越多越稳定，但对速度变化响应越慢
                         - 建议3-7个，平衡稳定性和响应性
        """
        self.position_history = deque(maxlen=history_size)  # 位置历史队列
        self.velocity_history = deque(maxlen=history_size-1)  # 速度历史队列

    def add_position(self, position: Point):
        """
        添加新的位置点到历史记录

        Args:
            position: 包含时间戳的位置点

        核心逻辑：
        1. 如果有历史位置，计算与上一个位置的速度矢量
        2. 速度 = 位移 / 时间间隔
        3. 将新位置添加到历史队列中
        """
        if len(self.position_history) > 0:
            # 计算与上一个位置的速度矢量
            last_pos = self.position_history[-1]
            dt = position.timestamp - last_pos.timestamp
            if dt > 0:  # 确保时间间隔有效
                vx = (position.x - last_pos.x) / dt  # X方向速度
                vy = (position.y - last_pos.y) / dt  # Y方向速度
                self.velocity_history.append((vx, vy, dt))

        self.position_history.append(position)
    
    def predict_position(self, prediction_time: float) -> Point:
        """
        预测未来位置

        Args:
            prediction_time: 预测的时间长度(秒)

        Returns:
            预测的位置点

        核心算法：加权线性预测
        1. 收集历史速度数据
        2. 使用加权平均计算当前速度趋势
        3. 基于速度趋势进行线性外推

        加权策略：
        - 越新的数据权重越高（时间衰减）
        - 权重 = (索引 + 1) / 总数量
        - 这样可以更好地反映最新的运动趋势
        """
        if len(self.position_history) == 0:
            return Point(0, 0)

        if len(self.position_history) < 2 or not self.velocity_history:
            # 数据不足时返回最后已知位置
            return self.position_history[-1]

        # 使用加权平均计算速度趋势
        total_weight = 0
        weighted_vx = 0
        weighted_vy = 0

        for i, (vx, vy, _dt) in enumerate(self.velocity_history):
            # 越新的数据权重越高
            weight = (i + 1) / len(self.velocity_history)
            weighted_vx += vx * weight
            weighted_vy += vy * weight
            total_weight += weight

        if total_weight > 0:
            avg_vx = weighted_vx / total_weight  # 平均X方向速度
            avg_vy = weighted_vy / total_weight  # 平均Y方向速度
        else:
            avg_vx = avg_vy = 0

        # 线性预测：当前位置 + 速度 × 时间
        last_pos = self.position_history[-1]
        predicted_x = last_pos.x + avg_vx * prediction_time
        predicted_y = last_pos.y + avg_vy * prediction_time

        return Point(predicted_x, predicted_y, last_pos.timestamp + prediction_time)
    
    def get_velocity_info(self) -> dict:
        """
        获取速度信息用于调试和性能分析

        Returns:
            包含速度统计信息的字典
        """
        if not self.velocity_history:
            return {"avg_speed": 0, "max_speed": 0, "direction": 0}

        speeds = []
        for vx, vy, _dt in self.velocity_history:
            speed = math.sqrt(vx*vx + vy*vy)  # 计算速度大小
            speeds.append(speed)

        avg_speed = sum(speeds) / len(speeds)  # 平均速度
        max_speed = max(speeds)  # 最大速度

        # 最新的运动方向（角度）
        if self.velocity_history:
            vx, vy, _dt = self.velocity_history[-1]
            direction = math.atan2(vy, vx) * 180 / math.pi  # 转换为角度
        else:
            direction = 0

        return {
            "avg_speed": avg_speed,
            "max_speed": max_speed,
            "direction": direction,
            "sample_count": len(self.velocity_history)
        }


class MovementSmoother:
    """移动平滑器

    核心原理：实现平滑的鼠标移动，避免抖动和不自然的移动

    算法思路：
    1. 速度平滑：使用指数移动平均平滑速度变化
    2. 方向渐变：避免突然的方向改变
    3. 距离限制：防止单次移动距离过大

    关键技术：
    - 指数移动平均：新速度 = 历史速度 × 平滑系数 + 理想速度 × (1-平滑系数)
    - 这样可以保持移动的连续性，避免突然的速度跳跃
    """

    def __init__(self, smoothing_factor: float = 0.3):
        """
        初始化平滑器

        Args:
            smoothing_factor: 平滑系数 (0-1)
                            - 越大越平滑但响应越慢
                            - 越小响应越快但可能产生抖动
                            - 建议值：0.2-0.5
        """
        self.smoothing_factor = smoothing_factor
        self.last_velocity = Point(0, 0)  # 上一次的速度矢量

    def smooth_movement(self, current_pos: Point, target_pos: Point,
                       max_speed: float, dt: float) -> Point:
        """
        计算平滑移动后的新位置

        Args:
            current_pos: 当前位置
            target_pos: 目标位置
            max_speed: 最大移动速度(像素/秒)
            dt: 时间间隔(秒)

        Returns:
            平滑移动后的新位置

        核心算法：
        1. 计算理想移动方向和速度
        2. 使用指数移动平均平滑速度
        3. 限制单次移动距离
        4. 计算最终位置
        """
        # 步骤1：计算目标方向和距离
        dx = target_pos.x - current_pos.x  # X方向位移
        dy = target_pos.y - current_pos.y  # Y方向位移
        distance = math.sqrt(dx*dx + dy*dy)  # 总距离

        if distance < 0.1:  # 避免除零错误和微小移动
            return current_pos

        # 步骤2：计算理想速度矢量（单位方向 × 最大速度）
        ideal_vx = (dx / distance) * max_speed  # 理想X方向速度
        ideal_vy = (dy / distance) * max_speed  # 理想Y方向速度

        # 步骤3：应用指数移动平均平滑速度
        # 公式：新速度 = 历史速度 × α + 理想速度 × (1-α)
        smooth_vx = self.last_velocity.x * self.smoothing_factor + ideal_vx * (1 - self.smoothing_factor)
        smooth_vy = self.last_velocity.y * self.smoothing_factor + ideal_vy * (1 - self.smoothing_factor)

        # 步骤4：更新速度历史
        self.last_velocity.x = smooth_vx
        self.last_velocity.y = smooth_vy

        # 步骤5：计算本次移动距离
        move_x = smooth_vx * dt  # X方向移动距离
        move_y = smooth_vy * dt  # Y方向移动距离

        # 步骤6：限制单次移动距离（防止跳跃）
        move_distance = math.sqrt(move_x*move_x + move_y*move_y)
        max_move = min(distance, max_speed * dt)  # 取距离和速度限制的较小值

        if move_distance > max_move:
            # 按比例缩放移动距离
            scale = max_move / move_distance
            move_x *= scale
            move_y *= scale

        # 步骤7：返回新位置
        return Point(current_pos.x + move_x, current_pos.y + move_y)


class AimController:
    """AI自瞄控制器

    核心控制器类，整合预测和平滑算法，提供完整的瞄准控制解决方案

    整体架构：
    输入目标位置 → 目标预测 → 平滑移动 → 输出瞄准位置

    工作流程：
    1. 接收AI推理结果（目标位置）
    2. 使用预测器预测目标未来位置（可选）
    3. 使用平滑器计算平滑移动轨迹
    4. 输出最终的瞄准位置

    关键特性：
    - 延时补偿：通过预测算法补偿AI推理延时
    - 平滑移动：避免抖动，提供自然的移动轨迹
    - 参数可调：支持实时调整各种控制参数
    - 性能监控：提供详细的统计信息
    """

    def __init__(self, config: ControllerConfig):
        """
        初始化控制器

        Args:
            config: 控制器配置参数
        """
        self.config = config
        self.current_position = Point(400, 300)  # 当前瞄准位置
        self.target_position = Point(400, 300)   # 目标位置

        # 子组件初始化
        self.predictor = TargetPredictor()  # 目标预测器
        self.smoother = MovementSmoother(config.smoothing_factor)  # 移动平滑器

        # 状态跟踪
        self.last_update_time = time.time()  # 上次更新时间
        self.total_distance_moved = 0.0  # 总移动距离
        self.update_count = 0  # 更新次数
    
    def update_target(self, target_pos: Point):
        """
        更新目标位置

        Args:
            target_pos: 新的目标位置（通常来自AI推理结果）

        说明：
        - 每次AI推理完成后调用此方法
        - 会自动将位置添加到预测器的历史记录中
        """
        self.target_position = target_pos
        self.predictor.add_position(target_pos)

    def update_position(self) -> Point:
        """
        更新AI瞄准点位置

        Returns:
            更新后的瞄准位置

        核心算法流程：
        1. 计算时间间隔（用于速度计算）
        2. 决定使用原始目标还是预测目标
        3. 检查是否需要移动（距离阈值）
        4. 使用平滑器计算新位置
        5. 更新统计信息
        """
        current_time = time.time()
        dt = current_time - self.last_update_time
        self.last_update_time = current_time

        # 防止异常的时间间隔（可能由于系统卡顿等原因）
        if dt > 0.1:  # 超过100ms认为是异常
            dt = 0.016  # 使用60fps的标准间隔

        # 获取有效目标位置（原始位置或预测位置）
        if self.config.prediction_enabled:
            # 使用预测位置补偿延时
            effective_target = self.predictor.predict_position(self.config.prediction_time)
        else:
            # 直接使用原始目标位置
            effective_target = self.target_position
        
        # 计算到目标的距离
        distance_to_target = math.sqrt(
            (effective_target.x - self.current_position.x)**2 +
            (effective_target.y - self.current_position.y)**2
        )

        # 只有距离大于阈值时才移动（避免微小抖动）
        if distance_to_target > self.config.min_move_threshold:
            # 计算最大移动速度（像素/秒）
            # 公式：基础速度 × 速度系数 ÷ 时间间隔
            max_speed = self.config.max_move_distance * self.config.move_speed / dt

            # 使用平滑器计算新位置
            new_position = self.smoother.smooth_movement(
                self.current_position, effective_target, max_speed, dt
            )

            # 更新统计信息
            move_distance = math.sqrt(
                (new_position.x - self.current_position.x)**2 +
                (new_position.y - self.current_position.y)**2
            )
            self.total_distance_moved += move_distance

            # 更新当前位置
            self.current_position = new_position

        self.update_count += 1
        return self.current_position
    
    def get_performance_stats(self) -> dict:
        """
        获取性能统计信息

        Returns:
            包含各种性能指标的字典
        """
        return {
            "current_position": (self.current_position.x, self.current_position.y),
            "target_position": (self.target_position.x, self.target_position.y),
            "total_distance_moved": self.total_distance_moved,
            "update_count": self.update_count,
            "avg_move_per_update": self.total_distance_moved / max(1, self.update_count),
            "predictor_stats": self.predictor.get_velocity_info()
        }

    def reset_stats(self):
        """重置统计信息"""
        self.total_distance_moved = 0.0
        self.update_count = 0
        self.last_update_time = time.time()

    def set_position(self, position: Point):
        """
        直接设置当前位置（用于初始化或重置）

        Args:
            position: 新的位置
        """
        self.current_position = position
